<?php
$title = 'Edit Guru - ' . APP_NAME;
ob_start();
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">Edit Guru</h1>
                    <p class="text-muted">Edit data guru: <?= htmlspecialchars($guru['nama']) ?></p>
                </div>
                <a href="<?= APP_URL ?>/admin/guru" class="btn btn-secondary">
                    <i class="bi bi-arrow-left"></i> Kembali
                </a>
            </div>
        </div>
    </div>
    
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-body">
                    <?php if (!empty($error)): ?>
                        <div class="alert alert-danger alert-dismissible fade show">
                            <i class="bi bi-exclamation-circle"></i>
                            <?= htmlspecialchars($error) ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($success)): ?>
                        <div class="alert alert-success alert-dismissible fade show">
                            <i class="bi bi-check-circle"></i>
                            <?= htmlspecialchars($success) ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <form method="POST" action="<?= APP_URL ?>/admin/guru/edit/<?= $guru['id'] ?>" class="needs-validation" novalidate>
                        <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">
                        
                        <div class="row">
                            <div class="col-md-6">
                                <h5 class="mb-3">Data Guru</h5>
                                
                                <div class="mb-3">
                                    <label for="nip" class="form-label">NIP <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="nip" name="nip" 
                                           value="<?= htmlspecialchars($_POST['nip'] ?? $guru['nip']) ?>" required>
                                    <div class="invalid-feedback">NIP wajib diisi</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="nama" class="form-label">Nama Lengkap <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="nama" name="nama" 
                                           value="<?= htmlspecialchars($_POST['nama'] ?? $guru['nama']) ?>" required>
                                    <div class="invalid-feedback">Nama lengkap wajib diisi</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="mata_pelajaran" class="form-label">Mata Pelajaran</label>
                                    <input type="text" class="form-control" id="mata_pelajaran" name="mata_pelajaran" 
                                           value="<?= htmlspecialchars($_POST['mata_pelajaran'] ?? $guru['mata_pelajaran']) ?>"
                                           placeholder="Contoh: Matematika, Bahasa Indonesia">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="no_hp" class="form-label">No. HP</label>
                                    <input type="text" class="form-control" id="no_hp" name="no_hp" 
                                           value="<?= htmlspecialchars($_POST['no_hp'] ?? $guru['no_hp']) ?>"
                                           placeholder="Contoh: 081234567890">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="alamat" class="form-label">Alamat</label>
                                    <textarea class="form-control" id="alamat" name="alamat" rows="3"
                                              placeholder="Alamat lengkap guru"><?= htmlspecialchars($_POST['alamat'] ?? $guru['alamat']) ?></textarea>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <h5 class="mb-3">Data Akun Login</h5>
                                
                                <div class="mb-3">
                                    <label for="username" class="form-label">Username <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="username" name="username" 
                                           value="<?= htmlspecialchars($_POST['username'] ?? $guru['username']) ?>" required>
                                    <div class="form-text">Username untuk login ke sistem</div>
                                    <div class="invalid-feedback">Username wajib diisi</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="password" class="form-label">Password Baru</label>
                                    <div class="input-group">
                                        <input type="password" class="form-control" id="password" name="password">
                                        <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                    </div>
                                    <div class="form-text">Kosongkan jika tidak ingin mengubah password. Minimal 6 karakter jika diisi.</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="email" class="form-label">Email</label>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           value="<?= htmlspecialchars($_POST['email'] ?? $guru['email']) ?>"
                                           placeholder="<EMAIL>">
                                    <div class="invalid-feedback">Format email tidak valid</div>
                                </div>
                                
                                <div class="alert alert-info">
                                    <i class="bi bi-info-circle"></i>
                                    <strong>Status Akun:</strong><br>
                                    <?php if ($guru['is_active'] && $guru['user_active']): ?>
                                        <span class="badge bg-success">Aktif</span> - Guru dapat login ke sistem
                                    <?php else: ?>
                                        <span class="badge bg-danger">Nonaktif</span> - Guru tidak dapat login
                                    <?php endif; ?>
                                </div>
                                
                                <div class="alert alert-warning">
                                    <i class="bi bi-exclamation-triangle"></i>
                                    <strong>Perhatian:</strong><br>
                                    Perubahan data akan mempengaruhi akses login guru ke sistem.
                                </div>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <div class="d-flex justify-content-between">
                            <a href="<?= APP_URL ?>/admin/guru" class="btn btn-secondary">
                                <i class="bi bi-arrow-left"></i> Batal
                            </a>
                            <button type="submit" class="btn btn-primary" data-original-text="Update Data">
                                <i class="bi bi-save"></i> Update Data
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('togglePassword').addEventListener('click', function() {
    const password = document.getElementById('password');
    const icon = this.querySelector('i');
    
    if (password.type === 'password') {
        password.type = 'text';
        icon.classList.remove('bi-eye');
        icon.classList.add('bi-eye-slash');
    } else {
        password.type = 'password';
        icon.classList.remove('bi-eye-slash');
        icon.classList.add('bi-eye');
    }
});
</script>

<?php
$content = ob_get_clean();
include VIEWS_PATH . '/layouts/main.php';
?>
