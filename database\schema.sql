-- Database schema untuk Sistem Absensi Siswa
-- Buat database
CREATE DATABASE IF NOT EXISTS absensi_siswa CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE absensi_siswa;

-- Tabel users (untuk login admin dan guru)
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role ENUM('admin', 'guru') NOT NULL,
    nama VARCHAR(100) NOT NULL,
    email VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE
);

-- Tabel kelas
CREATE TABLE kelas (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nama_kelas VARCHAR(50) NOT NULL,
    tingkat VARCHAR(10) NOT NULL,
    j<PERSON><PERSON>(50),
    tahun_ajaran VARCHAR(20) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE
);

-- Tabel guru
CREATE TABLE guru (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    nip VARCHAR(30) UNIQUE,
    nama VARCHAR(100) NOT NULL,
    mata_pelajaran VARCHAR(100),
    no_hp VARCHAR(20),
    alamat TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Tabel relasi guru dengan kelas yang diajar
CREATE TABLE guru_kelas (
    id INT AUTO_INCREMENT PRIMARY KEY,
    guru_id INT NOT NULL,
    kelas_id INT NOT NULL,
    mata_pelajaran VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (guru_id) REFERENCES guru(id) ON DELETE CASCADE,
    FOREIGN KEY (kelas_id) REFERENCES kelas(id) ON DELETE CASCADE,
    UNIQUE KEY unique_guru_kelas (guru_id, kelas_id)
);

-- Tabel murid
CREATE TABLE murid (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nis VARCHAR(20) UNIQUE NOT NULL,
    nama VARCHAR(100) NOT NULL,
    kelas_id INT NOT NULL,
    jenis_kelamin ENUM('L', 'P') NOT NULL,
    tempat_lahir VARCHAR(50),
    tanggal_lahir DATE,
    alamat TEXT,
    no_hp_ortu VARCHAR(20),
    nama_ortu VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (kelas_id) REFERENCES kelas(id) ON DELETE RESTRICT
);

-- Tabel nilai dan catatan
CREATE TABLE nilai_catatan (
    id INT AUTO_INCREMENT PRIMARY KEY,
    murid_id INT NOT NULL,
    guru_id INT NOT NULL,
    mata_pelajaran VARCHAR(100),
    jenis ENUM('nilai', 'catatan', 'absensi') NOT NULL,
    nilai DECIMAL(5,2) NULL,
    catatan TEXT NULL,
    tanggal DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (murid_id) REFERENCES murid(id) ON DELETE CASCADE,
    FOREIGN KEY (guru_id) REFERENCES guru(id) ON DELETE CASCADE
);

-- Insert data default admin
INSERT INTO users (username, password, role, nama, email) VALUES 
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 'Administrator', '<EMAIL>');

-- Insert data kelas contoh
INSERT INTO kelas (nama_kelas, tingkat, jurusan, tahun_ajaran) VALUES 
('X-1', '10', 'IPA', '2024/2025'),
('X-2', '10', 'IPS', '2024/2025'),
('XI-1', '11', 'IPA', '2024/2025'),
('XI-2', '11', 'IPS', '2024/2025'),
('XII-1', '12', 'IPA', '2024/2025'),
('XII-2', '12', 'IPS', '2024/2025');

-- Insert data guru contoh
INSERT INTO users (username, password, role, nama, email) VALUES 
('guru1', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'guru', 'Budi Santoso', '<EMAIL>'),
('guru2', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'guru', 'Siti Aminah', '<EMAIL>');

INSERT INTO guru (user_id, nip, nama, mata_pelajaran, no_hp) VALUES 
(2, '123456789', 'Budi Santoso', 'Matematika', '081234567890'),
(3, '987654321', 'Siti Aminah', 'Bahasa Indonesia', '081234567891');

-- Relasi guru dengan kelas
INSERT INTO guru_kelas (guru_id, kelas_id, mata_pelajaran) VALUES 
(1, 1, 'Matematika'),
(1, 2, 'Matematika'),
(2, 1, 'Bahasa Indonesia'),
(2, 3, 'Bahasa Indonesia');

-- Insert data murid contoh
INSERT INTO murid (nis, nama, kelas_id, jenis_kelamin, tempat_lahir, tanggal_lahir, alamat, no_hp_ortu, nama_ortu) VALUES 
('2024001', 'Ahmad Fauzi', 1, 'L', 'Jakarta', '2008-01-15', 'Jl. Merdeka No. 1', '081234567892', 'Bapak Ahmad'),
('2024002', 'Sari Dewi', 1, 'P', 'Bandung', '2008-02-20', 'Jl. Sudirman No. 2', '081234567893', 'Ibu Sari'),
('2024003', 'Rudi Hartono', 2, 'L', 'Surabaya', '2008-03-10', 'Jl. Gatot Subroto No. 3', '081234567894', 'Bapak Rudi'),
('2024004', 'Maya Sari', 2, 'P', 'Medan', '2008-04-05', 'Jl. Ahmad Yani No. 4', '081234567895', 'Ibu Maya');
