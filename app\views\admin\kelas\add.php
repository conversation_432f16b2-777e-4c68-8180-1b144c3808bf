<?php
$title = '<PERSON><PERSON> Kelas - ' . APP_NAME;
ob_start();
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">Tambah Kelas</h1>
                    <p class="text-muted">Tambahkan kelas baru ke sistem</p>
                </div>
                <a href="<?= APP_URL ?>/admin/kelas" class="btn btn-secondary">
                    <i class="bi bi-arrow-left"></i> Kembali
                </a>
            </div>
        </div>
    </div>
    
    <div class="row justify-content-center">
        <div class="col-lg-6">
            <div class="card">
                <div class="card-body">
                    <?php if (!empty($error)): ?>
                        <div class="alert alert-danger alert-dismissible fade show">
                            <i class="bi bi-exclamation-circle"></i>
                            <?= htmlspecialchars($error) ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($success)): ?>
                        <div class="alert alert-success alert-dismissible fade show">
                            <i class="bi bi-check-circle"></i>
                            <?= htmlspecialchars($success) ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <form method="POST" action="<?= APP_URL ?>/admin/kelas/add" class="needs-validation" novalidate>
                        <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">
                        
                        <div class="mb-3">
                            <label for="nama_kelas" class="form-label">Nama Kelas <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="nama_kelas" name="nama_kelas" 
                                   value="<?= htmlspecialchars($_POST['nama_kelas'] ?? '') ?>" required
                                   placeholder="Contoh: X-1, XI-IPA-1, XII-IPS-2">
                            <div class="form-text">Nama kelas harus unik untuk setiap tingkat dan tahun ajaran</div>
                            <div class="invalid-feedback">Nama kelas wajib diisi</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="tingkat" class="form-label">Tingkat <span class="text-danger">*</span></label>
                            <select class="form-select" id="tingkat" name="tingkat" required>
                                <option value="">Pilih tingkat</option>
                                <option value="10" <?= ($_POST['tingkat'] ?? '') === '10' ? 'selected' : '' ?>>10 (Sepuluh)</option>
                                <option value="11" <?= ($_POST['tingkat'] ?? '') === '11' ? 'selected' : '' ?>>11 (Sebelas)</option>
                                <option value="12" <?= ($_POST['tingkat'] ?? '') === '12' ? 'selected' : '' ?>>12 (Dua Belas)</option>
                            </select>
                            <div class="invalid-feedback">Tingkat wajib dipilih</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="jurusan" class="form-label">Jurusan</label>
                            <input type="text" class="form-control" id="jurusan" name="jurusan" 
                                   value="<?= htmlspecialchars($_POST['jurusan'] ?? '') ?>"
                                   placeholder="Contoh: IPA, IPS, Bahasa, Teknik Informatika">
                            <div class="form-text">Kosongkan jika tidak ada pembagian jurusan</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="tahun_ajaran" class="form-label">Tahun Ajaran <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="tahun_ajaran" name="tahun_ajaran" 
                                   value="<?= htmlspecialchars($_POST['tahun_ajaran'] ?? date('Y') . '/' . (date('Y') + 1)) ?>" required
                                   placeholder="Contoh: 2024/2025">
                            <div class="form-text">Format: YYYY/YYYY (contoh: 2024/2025)</div>
                            <div class="invalid-feedback">Tahun ajaran wajib diisi</div>
                        </div>
                        
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle"></i>
                            <strong>Informasi:</strong><br>
                            • Nama kelas harus unik untuk setiap kombinasi tingkat dan tahun ajaran<br>
                            • Setelah kelas dibuat, Anda dapat menugaskan guru untuk mengajar di kelas ini<br>
                            • Murid dapat ditambahkan ke kelas melalui menu Data Murid
                        </div>
                        
                        <hr>
                        
                        <div class="d-flex justify-content-between">
                            <a href="<?= APP_URL ?>/admin/kelas" class="btn btn-secondary">
                                <i class="bi bi-arrow-left"></i> Batal
                            </a>
                            <button type="submit" class="btn btn-primary" data-original-text="Simpan Data">
                                <i class="bi bi-save"></i> Simpan Data
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-generate tahun ajaran
document.addEventListener('DOMContentLoaded', function() {
    const tahunAjaranInput = document.getElementById('tahun_ajaran');
    if (tahunAjaranInput.value === '') {
        const currentYear = new Date().getFullYear();
        const nextYear = currentYear + 1;
        tahunAjaranInput.value = currentYear + '/' + nextYear;
    }
});

// Validate tahun ajaran format
document.getElementById('tahun_ajaran').addEventListener('input', function() {
    const value = this.value;
    const pattern = /^\d{4}\/\d{4}$/;
    
    if (value && !pattern.test(value)) {
        this.setCustomValidity('Format tahun ajaran harus YYYY/YYYY');
    } else {
        this.setCustomValidity('');
    }
});
</script>

<?php
$content = ob_get_clean();
include VIEWS_PATH . '/layouts/main.php';
?>
