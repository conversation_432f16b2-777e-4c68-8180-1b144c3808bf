<?php
$title = 'Input Nilai - ' . $murid['nama'] . ' - ' . APP_NAME;
ob_start();
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="bi bi-pencil-square"></i>
                        Input Nilai & Catatan
                    </h1>
                    <p class="text-muted">
                        <?= htmlspecialchars($murid['nama']) ?> • 
                        <?= htmlspecialchars($murid['nama_kelas']) ?> • 
                        <?= htmlspecialchars($mataPelajaran) ?>
                    </p>
                </div>
                <a href="<?= APP_URL ?>/guru/murid/<?= $murid['kelas_id'] ?>" class="btn btn-secondary">
                    <i class="bi bi-arrow-left"></i> Ke<PERSON><PERSON>
                </a>
            </div>
        </div>
    </div>
    
    <!-- Student Info Card -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-light">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <h6 class="text-muted mb-1">NIS</h6>
                            <strong><?= htmlspecialchars($murid['nis']) ?></strong>
                        </div>
                        <div class="col-md-3">
                            <h6 class="text-muted mb-1">Nama Lengkap</h6>
                            <strong><?= htmlspecialchars($murid['nama']) ?></strong>
                        </div>
                        <div class="col-md-3">
                            <h6 class="text-muted mb-1">Kelas</h6>
                            <strong><?= htmlspecialchars($murid['nama_kelas']) ?></strong>
                        </div>
                        <div class="col-md-3">
                            <h6 class="text-muted mb-1">Jenis Kelamin</h6>
                            <strong><?= $murid['jenis_kelamin'] === 'L' ? 'Laki-laki' : 'Perempuan' ?></strong>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <!-- Input Form -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-plus-circle"></i>
                        Tambah Nilai/Catatan
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($error)): ?>
                        <div class="alert alert-danger alert-dismissible fade show">
                            <i class="bi bi-exclamation-circle"></i>
                            <?= htmlspecialchars($error) ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($success)): ?>
                        <div class="alert alert-success alert-dismissible fade show">
                            <i class="bi bi-check-circle"></i>
                            <?= htmlspecialchars($success) ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <form method="POST" action="<?= APP_URL ?>/guru/nilai/<?= $murid['id'] ?>" class="needs-validation" novalidate>
                        <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">
                        
                        <div class="mb-3">
                            <label for="jenis" class="form-label">Jenis Input <span class="text-danger">*</span></label>
                            <select class="form-select" id="jenis" name="jenis" required onchange="toggleInputFields()">
                                <option value="">Pilih jenis input</option>
                                <option value="nilai" <?= ($_POST['jenis'] ?? '') === 'nilai' ? 'selected' : '' ?>>Nilai</option>
                                <option value="catatan" <?= ($_POST['jenis'] ?? '') === 'catatan' ? 'selected' : '' ?>>Catatan</option>
                                <option value="absensi" <?= ($_POST['jenis'] ?? '') === 'absensi' ? 'selected' : '' ?>>Absensi</option>
                            </select>
                            <div class="invalid-feedback">Jenis input wajib dipilih</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="tanggal" class="form-label">Tanggal <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="tanggal" name="tanggal" 
                                   value="<?= $_POST['tanggal'] ?? date('Y-m-d') ?>" required>
                            <div class="invalid-feedback">Tanggal wajib diisi</div>
                        </div>
                        
                        <div class="mb-3" id="nilai-field" style="display: none;">
                            <label for="nilai" class="form-label">Nilai <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="nilai" name="nilai" 
                                   min="0" max="100" step="0.1"
                                   value="<?= $_POST['nilai'] ?? '' ?>"
                                   placeholder="Masukkan nilai (0-100)">
                            <div class="form-text">Nilai dalam skala 0-100</div>
                            <div class="invalid-feedback">Nilai wajib diisi dan harus berupa angka</div>
                        </div>
                        
                        <div class="mb-3" id="catatan-field" style="display: none;">
                            <label for="catatan" class="form-label">Catatan/Keterangan <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="catatan" name="catatan" rows="3"
                                      placeholder="Masukkan catatan atau keterangan"><?= $_POST['catatan'] ?? '' ?></textarea>
                            <div class="form-text">Catatan untuk murid (contoh: "Aktif bertanya", "Sakit", dll)</div>
                            <div class="invalid-feedback">Catatan wajib diisi</div>
                        </div>
                        
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle"></i>
                            <strong>Mata Pelajaran:</strong> <?= htmlspecialchars($mataPelajaran) ?>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="<?= APP_URL ?>/guru/murid/<?= $murid['kelas_id'] ?>" class="btn btn-secondary">
                                <i class="bi bi-arrow-left"></i> Batal
                            </a>
                            <button type="submit" class="btn btn-primary" data-original-text="Simpan Data">
                                <i class="bi bi-save"></i> Simpan Data
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- History -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-clock-history"></i>
                        Riwayat Nilai & Catatan
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($riwayat)): ?>
                        <div class="timeline">
                            <?php foreach ($riwayat as $item): ?>
                                <div class="timeline-item mb-3">
                                    <div class="card border-start border-4 <?= $item['jenis'] === 'nilai' ? 'border-success' : ($item['jenis'] === 'catatan' ? 'border-info' : 'border-warning') ?>">
                                        <div class="card-body p-3">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div>
                                                    <span class="badge <?= $item['jenis'] === 'nilai' ? 'bg-success' : ($item['jenis'] === 'catatan' ? 'bg-info' : 'bg-warning') ?>">
                                                        <?= ucfirst($item['jenis']) ?>
                                                    </span>
                                                    <?php if ($item['jenis'] === 'nilai'): ?>
                                                        <span class="badge bg-primary ms-1">
                                                            <?= $item['nilai'] ?>
                                                        </span>
                                                    <?php endif; ?>
                                                </div>
                                                <small class="text-muted">
                                                    <?= date('d/m/Y', strtotime($item['tanggal'])) ?>
                                                </small>
                                            </div>
                                            
                                            <?php if (!empty($item['catatan'])): ?>
                                                <div class="mt-2">
                                                    <small class="text-muted">Catatan:</small>
                                                    <p class="mb-0"><?= htmlspecialchars($item['catatan']) ?></p>
                                                </div>
                                            <?php endif; ?>
                                            
                                            <div class="mt-2">
                                                <small class="text-muted">
                                                    <i class="bi bi-book"></i> <?= htmlspecialchars($item['mata_pelajaran']) ?>
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="bi bi-clock-history text-muted" style="font-size: 3rem;"></i>
                            <h6 class="mt-3 text-muted">Belum Ada Riwayat</h6>
                            <p class="text-muted">Belum ada nilai atau catatan untuk murid ini</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function toggleInputFields() {
    const jenis = document.getElementById('jenis').value;
    const nilaiField = document.getElementById('nilai-field');
    const catatanField = document.getElementById('catatan-field');
    const nilaiInput = document.getElementById('nilai');
    const catatanInput = document.getElementById('catatan');
    
    // Hide all fields first
    nilaiField.style.display = 'none';
    catatanField.style.display = 'none';
    
    // Remove required attributes
    nilaiInput.removeAttribute('required');
    catatanInput.removeAttribute('required');
    
    // Show relevant field based on selection
    if (jenis === 'nilai') {
        nilaiField.style.display = 'block';
        nilaiInput.setAttribute('required', 'required');
    } else if (jenis === 'catatan' || jenis === 'absensi') {
        catatanField.style.display = 'block';
        catatanInput.setAttribute('required', 'required');
        
        // Update label for absensi
        const label = document.querySelector('label[for="catatan"]');
        if (jenis === 'absensi') {
            label.innerHTML = 'Keterangan Absensi <span class="text-danger">*</span>';
            catatanInput.placeholder = 'Masukkan keterangan absensi (Hadir, Sakit, Izin, Alpha)';
        } else {
            label.innerHTML = 'Catatan/Keterangan <span class="text-danger">*</span>';
            catatanInput.placeholder = 'Masukkan catatan atau keterangan';
        }
    }
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    toggleInputFields();
});
</script>

<?php
$content = ob_get_clean();
include VIEWS_PATH . '/layouts/main.php';
?>
