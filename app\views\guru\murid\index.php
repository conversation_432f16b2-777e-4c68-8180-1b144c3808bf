<?php
$title = '<PERSON><PERSON> ' . $kelas['nama_kelas'] . ' - ' . APP_NAME;
ob_start();
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="bi bi-people"></i>
                        <PERSON><PERSON> <?= htmlspecialchars($kelas['nama_kelas']) ?>
                    </h1>
                    <p class="text-muted">
                        <?= htmlspecialchars($kelas['mata_pelajaran']) ?> • 
                        Tingkat <?= htmlspecialchars($kelas['tingkat']) ?> • 
                        <?= htmlspecialchars($kelas['jurusan']) ?>
                    </p>
                </div>
                <div>
                    <a href="<?= APP_URL ?>/guru/kelas" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i> Kembali
                    </a>
                    <div class="btn-group ms-2">
                        <a href="<?= APP_URL ?>/guru/rekap/<?= $kelas['id'] ?>" 
                           class="btn btn-outline-primary">
                            <i class="bi bi-file-earmark-text"></i> Rekap
                        </a>
                        <button type="button" class="btn btn-outline-primary dropdown-toggle dropdown-toggle-split" 
                                data-bs-toggle="dropdown">
                            <span class="visually-hidden">Toggle Dropdown</span>
                        </button>
                        <ul class="dropdown-menu">
                            <li>
                                <a class="dropdown-item" href="<?= APP_URL ?>/guru/export/pdf/<?= $kelas['id'] ?>">
                                    <i class="bi bi-file-earmark-pdf text-danger"></i> Export PDF
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="<?= APP_URL ?>/guru/export/excel/<?= $kelas['id'] ?>">
                                    <i class="bi bi-file-earmark-excel text-success"></i> Export Excel
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Class Info Card -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <h6 class="card-subtitle mb-1">Kelas</h6>
                            <h5 class="card-title"><?= htmlspecialchars($kelas['nama_kelas']) ?></h5>
                        </div>
                        <div class="col-md-3">
                            <h6 class="card-subtitle mb-1">Mata Pelajaran</h6>
                            <h5 class="card-title"><?= htmlspecialchars($kelas['mata_pelajaran']) ?></h5>
                        </div>
                        <div class="col-md-3">
                            <h6 class="card-subtitle mb-1">Total Murid</h6>
                            <h5 class="card-title"><?= count($muridList) ?> orang</h5>
                        </div>
                        <div class="col-md-3">
                            <h6 class="card-subtitle mb-1">Tahun Ajaran</h6>
                            <h5 class="card-title"><?= htmlspecialchars($kelas['tahun_ajaran']) ?></h5>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Students List -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <?php if (!empty($muridList)): ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>No</th>
                                        <th>NIS</th>
                                        <th>Nama</th>
                                        <th>Jenis Kelamin</th>
                                        <th>Total Nilai</th>
                                        <th>Rata-rata</th>
                                        <th>Catatan</th>
                                        <th>Update Terakhir</th>
                                        <th>Aksi</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php 
                                    $no = 1;
                                    foreach ($muridList as $murid): 
                                    ?>
                                        <tr>
                                            <td><?= $no++ ?></td>
                                            <td>
                                                <strong><?= htmlspecialchars($murid['nis']) ?></strong>
                                            </td>
                                            <td>
                                                <div>
                                                    <strong><?= htmlspecialchars($murid['nama']) ?></strong>
                                                    <?php if (!empty($murid['tempat_lahir']) && !empty($murid['tanggal_lahir'])): ?>
                                                        <br><small class="text-muted">
                                                            <?= htmlspecialchars($murid['tempat_lahir']) ?>, 
                                                            <?= date('d/m/Y', strtotime($murid['tanggal_lahir'])) ?>
                                                        </small>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge <?= $murid['jenis_kelamin'] === 'L' ? 'bg-primary' : 'bg-pink' ?>">
                                                    <?= $murid['jenis_kelamin'] === 'L' ? 'Laki-laki' : 'Perempuan' ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">
                                                    <?= $murid['stats']['total_nilai'] ?? 0 ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if ($murid['stats']['rata_nilai']): ?>
                                                    <span class="badge bg-success">
                                                        <?= round($murid['stats']['rata_nilai'], 1) ?>
                                                    </span>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-warning">
                                                    <?= $murid['stats']['total_catatan'] ?? 0 ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if ($murid['stats']['last_update']): ?>
                                                    <small class="text-muted">
                                                        <?= date('d/m/Y', strtotime($murid['stats']['last_update'])) ?>
                                                    </small>
                                                <?php else: ?>
                                                    <small class="text-muted">Belum ada</small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="<?= APP_URL ?>/guru/nilai/<?= $murid['id'] ?>" 
                                                       class="btn btn-primary" 
                                                       data-bs-toggle="tooltip" title="Input Nilai/Catatan">
                                                        <i class="bi bi-pencil-square"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- Quick Stats -->
                        <div class="row mt-4">
                            <div class="col-md-12">
                                <div class="alert alert-light">
                                    <div class="row text-center">
                                        <div class="col-md-3">
                                            <strong class="text-primary"><?= count($muridList) ?></strong>
                                            <br><small class="text-muted">Total Murid</small>
                                        </div>
                                        <div class="col-md-3">
                                            <strong class="text-success">
                                                <?= count(array_filter($muridList, function($m) { return ($m['stats']['total_nilai'] ?? 0) > 0; })) ?>
                                            </strong>
                                            <br><small class="text-muted">Sudah Dinilai</small>
                                        </div>
                                        <div class="col-md-3">
                                            <strong class="text-info">
                                                <?= array_sum(array_column(array_column($muridList, 'stats'), 'total_nilai')) ?>
                                            </strong>
                                            <br><small class="text-muted">Total Nilai</small>
                                        </div>
                                        <div class="col-md-3">
                                            <strong class="text-warning">
                                                <?= array_sum(array_column(array_column($muridList, 'stats'), 'total_catatan')) ?>
                                            </strong>
                                            <br><small class="text-muted">Total Catatan</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="bi bi-people text-muted" style="font-size: 4rem;"></i>
                            <h5 class="mt-3 text-muted">Belum Ada Murid</h5>
                            <p class="text-muted">Kelas ini belum memiliki murid yang terdaftar</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.bg-pink {
    background-color: #e91e63 !important;
}
</style>

<?php
$content = ob_get_clean();
include VIEWS_PATH . '/layouts/main.php';
?>
