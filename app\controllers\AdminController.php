<?php

class AdminController extends Controller
{
    private $userModel;
    private $guruModel;
    private $kelasModel;
    private $muridModel;
    private $nilaiCatatanModel;
    
    public function __construct()
    {
        parent::__construct();
        $this->requireRole('admin');
        
        $this->userModel = new User();
        $this->guruModel = new Guru();
        $this->kelasModel = new Kelas();
        $this->muridModel = new Murid();
        $this->nilaiCatatanModel = new NilaiCatatan();
    }
    
    // GURU MANAGEMENT
    public function guru()
    {
        $page = $_GET['page'] ?? 1;
        $search = $_GET['search'] ?? '';
        
        $conditions = 'g.is_active = 1';
        $params = [];
        
        if (!empty($search)) {
            $conditions .= ' AND (g.nama LIKE :search OR g.nip LIKE :search OR u.username LIKE :search)';
            $params['search'] = "%{$search}%";
        }
        
        $sql = "SELECT g.*, u.username, u.email, u.is_active as user_active
                FROM guru g
                LEFT JOIN users u ON g.user_id = u.id
                WHERE {$conditions}
                ORDER BY g.nama";
        
        $totalSql = "SELECT COUNT(*) as total FROM guru g LEFT JOIN users u ON g.user_id = u.id WHERE {$conditions}";
        $total = $this->db->fetch($totalSql, $params)['total'];
        
        $offset = ($page - 1) * RECORDS_PER_PAGE;
        $guru = $this->db->fetchAll($sql . " LIMIT " . RECORDS_PER_PAGE . " OFFSET {$offset}", $params);
        
        $pagination = [
            'current_page' => $page,
            'total' => $total,
            'per_page' => RECORDS_PER_PAGE,
            'last_page' => ceil($total / RECORDS_PER_PAGE)
        ];
        
        $this->view('admin/guru/index', [
            'guru' => $guru,
            'pagination' => $pagination,
            'search' => $search
        ]);
    }
    
    public function addGuru()
    {
        $error = '';
        $success = '';
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            try {
                $this->validateCSRF();
                
                $guruData = [
                    'nip' => $this->sanitize($_POST['nip']),
                    'nama' => $this->sanitize($_POST['nama']),
                    'mata_pelajaran' => $this->sanitize($_POST['mata_pelajaran']),
                    'no_hp' => $this->sanitize($_POST['no_hp']),
                    'alamat' => $this->sanitize($_POST['alamat'])
                ];
                
                $userData = [
                    'username' => $this->sanitize($_POST['username']),
                    'password' => $_POST['password'],
                    'role' => 'guru',
                    'nama' => $guruData['nama'],
                    'email' => $this->sanitize($_POST['email'])
                ];
                
                // Validation
                $errors = $this->validate(array_merge($guruData, $userData), [
                    'nip' => 'required',
                    'nama' => 'required|min:3',
                    'username' => 'required|min:3',
                    'password' => 'required|min:6',
                    'email' => 'email'
                ]);
                
                if (empty($errors)) {
                    // Check unique constraints
                    if ($this->guruModel->isNipExists($guruData['nip'])) {
                        $errors['nip'] = 'NIP sudah digunakan';
                    }
                    
                    if ($this->userModel->isUsernameExists($userData['username'])) {
                        $errors['username'] = 'Username sudah digunakan';
                    }
                    
                    if (empty($errors)) {
                        $this->guruModel->createGuruWithUser($guruData, $userData);
                        $success = 'Data guru berhasil ditambahkan';
                        
                        // Reset form
                        $_POST = [];
                    }
                }
                
                if (!empty($errors)) {
                    $error = implode(', ', $errors);
                }
                
            } catch (Exception $e) {
                $error = 'Terjadi kesalahan: ' . $e->getMessage();
            }
        }
        
        $this->view('admin/guru/add', [
            'error' => $error,
            'success' => $success,
            'csrf_token' => $this->generateCSRF()
        ]);
    }
    
    public function editGuru($id)
    {
        $guru = $this->guruModel->getGuruWithUser($id);
        if (!$guru) {
            $this->redirect('admin/guru');
        }
        
        $error = '';
        $success = '';
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            try {
                $this->validateCSRF();
                
                $guruData = [
                    'nip' => $this->sanitize($_POST['nip']),
                    'nama' => $this->sanitize($_POST['nama']),
                    'mata_pelajaran' => $this->sanitize($_POST['mata_pelajaran']),
                    'no_hp' => $this->sanitize($_POST['no_hp']),
                    'alamat' => $this->sanitize($_POST['alamat'])
                ];
                
                $userData = [
                    'username' => $this->sanitize($_POST['username']),
                    'nama' => $guruData['nama'],
                    'email' => $this->sanitize($_POST['email'])
                ];
                
                if (!empty($_POST['password'])) {
                    $userData['password'] = $_POST['password'];
                }
                
                // Validation
                $errors = $this->validate(array_merge($guruData, $userData), [
                    'nip' => 'required',
                    'nama' => 'required|min:3',
                    'username' => 'required|min:3',
                    'email' => 'email'
                ]);
                
                if (!empty($_POST['password'])) {
                    $passwordErrors = $this->validate(['password' => $_POST['password']], ['password' => 'min:6']);
                    $errors = array_merge($errors, $passwordErrors);
                }
                
                if (empty($errors)) {
                    // Check unique constraints
                    if ($this->guruModel->isNipExists($guruData['nip'], $id)) {
                        $errors['nip'] = 'NIP sudah digunakan';
                    }
                    
                    if ($this->userModel->isUsernameExists($userData['username'], $guru['user_id'])) {
                        $errors['username'] = 'Username sudah digunakan';
                    }
                    
                    if (empty($errors)) {
                        $this->guruModel->updateGuruWithUser($id, $guruData, $userData);
                        $success = 'Data guru berhasil diperbarui';
                        
                        // Refresh data
                        $guru = $this->guruModel->getGuruWithUser($id);
                    }
                }
                
                if (!empty($errors)) {
                    $error = implode(', ', $errors);
                }
                
            } catch (Exception $e) {
                $error = 'Terjadi kesalahan: ' . $e->getMessage();
            }
        }
        
        $this->view('admin/guru/edit', [
            'guru' => $guru,
            'error' => $error,
            'success' => $success,
            'csrf_token' => $this->generateCSRF()
        ]);
    }
    
    public function deleteGuru($id)
    {
        try {
            $guru = $this->guruModel->find($id);
            if ($guru) {
                $this->guruModel->softDelete($id);
                
                // Also deactivate user account
                if ($guru['user_id']) {
                    $this->userModel->update($guru['user_id'], ['is_active' => 0]);
                }
            }
        } catch (Exception $e) {
            // Handle error silently for now
        }
        
        $this->redirect('admin/guru');
    }

    // KELAS MANAGEMENT
    public function kelas()
    {
        $page = $_GET['page'] ?? 1;
        $search = $_GET['search'] ?? '';

        $conditions = 'k.is_active = 1';
        $params = [];

        if (!empty($search)) {
            $conditions .= ' AND (k.nama_kelas LIKE :search OR k.tingkat LIKE :search OR k.jurusan LIKE :search)';
            $params['search'] = "%{$search}%";
        }

        $sql = "SELECT k.*,
                COUNT(m.id) as total_murid,
                COUNT(DISTINCT gk.guru_id) as total_guru
                FROM kelas k
                LEFT JOIN murid m ON k.id = m.kelas_id AND m.is_active = 1
                LEFT JOIN guru_kelas gk ON k.id = gk.kelas_id
                WHERE {$conditions}
                GROUP BY k.id
                ORDER BY k.tingkat, k.nama_kelas";

        $totalSql = "SELECT COUNT(*) as total FROM kelas k WHERE {$conditions}";
        $total = $this->db->fetch($totalSql, $params)['total'];

        $offset = ($page - 1) * RECORDS_PER_PAGE;
        $kelas = $this->db->fetchAll($sql . " LIMIT " . RECORDS_PER_PAGE . " OFFSET {$offset}", $params);

        $pagination = [
            'current_page' => $page,
            'total' => $total,
            'per_page' => RECORDS_PER_PAGE,
            'last_page' => ceil($total / RECORDS_PER_PAGE)
        ];

        $this->view('admin/kelas/index', [
            'kelas' => $kelas,
            'pagination' => $pagination,
            'search' => $search
        ]);
    }

    public function addKelas()
    {
        $error = '';
        $success = '';

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            try {
                $this->validateCSRF();

                $data = [
                    'nama_kelas' => $this->sanitize($_POST['nama_kelas']),
                    'tingkat' => $this->sanitize($_POST['tingkat']),
                    'jurusan' => $this->sanitize($_POST['jurusan']),
                    'tahun_ajaran' => $this->sanitize($_POST['tahun_ajaran'])
                ];

                // Validation
                $errors = $this->validate($data, [
                    'nama_kelas' => 'required',
                    'tingkat' => 'required',
                    'tahun_ajaran' => 'required'
                ]);

                if (empty($errors)) {
                    // Check if class already exists
                    if ($this->kelasModel->isNamaKelasExists($data['nama_kelas'], $data['tingkat'], $data['tahun_ajaran'])) {
                        $errors['nama_kelas'] = 'Kelas dengan nama, tingkat, dan tahun ajaran yang sama sudah ada';
                    }

                    if (empty($errors)) {
                        $this->kelasModel->create($data);
                        $success = 'Data kelas berhasil ditambahkan';

                        // Reset form
                        $_POST = [];
                    }
                }

                if (!empty($errors)) {
                    $error = implode(', ', $errors);
                }

            } catch (Exception $e) {
                $error = 'Terjadi kesalahan: ' . $e->getMessage();
            }
        }

        $this->view('admin/kelas/add', [
            'error' => $error,
            'success' => $success,
            'csrf_token' => $this->generateCSRF()
        ]);
    }

    public function editKelas($id)
    {
        $kelas = $this->kelasModel->find($id);
        if (!$kelas) {
            $this->redirect('admin/kelas');
        }

        $error = '';
        $success = '';

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            try {
                $this->validateCSRF();

                $data = [
                    'nama_kelas' => $this->sanitize($_POST['nama_kelas']),
                    'tingkat' => $this->sanitize($_POST['tingkat']),
                    'jurusan' => $this->sanitize($_POST['jurusan']),
                    'tahun_ajaran' => $this->sanitize($_POST['tahun_ajaran'])
                ];

                // Validation
                $errors = $this->validate($data, [
                    'nama_kelas' => 'required',
                    'tingkat' => 'required',
                    'tahun_ajaran' => 'required'
                ]);

                if (empty($errors)) {
                    // Check if class already exists
                    if ($this->kelasModel->isNamaKelasExists($data['nama_kelas'], $data['tingkat'], $data['tahun_ajaran'], $id)) {
                        $errors['nama_kelas'] = 'Kelas dengan nama, tingkat, dan tahun ajaran yang sama sudah ada';
                    }

                    if (empty($errors)) {
                        $this->kelasModel->update($id, $data);
                        $success = 'Data kelas berhasil diperbarui';

                        // Refresh data
                        $kelas = $this->kelasModel->find($id);
                    }
                }

                if (!empty($errors)) {
                    $error = implode(', ', $errors);
                }

            } catch (Exception $e) {
                $error = 'Terjadi kesalahan: ' . $e->getMessage();
            }
        }

        $this->view('admin/kelas/edit', [
            'kelas' => $kelas,
            'error' => $error,
            'success' => $success,
            'csrf_token' => $this->generateCSRF()
        ]);
    }

    public function deleteKelas($id)
    {
        try {
            // Check if there are students in this class
            $muridCount = $this->muridModel->count('kelas_id = :kelas_id AND is_active = 1', ['kelas_id' => $id]);

            if ($muridCount > 0) {
                // Cannot delete class with students
                $this->redirect('admin/kelas?error=cannot_delete_class_with_students');
                return;
            }

            $this->kelasModel->softDelete($id);
        } catch (Exception $e) {
            // Handle error silently for now
        }

        $this->redirect('admin/kelas');
    }

    // MURID MANAGEMENT
    public function murid()
    {
        $page = $_GET['page'] ?? 1;
        $search = $_GET['search'] ?? '';
        $kelasFilter = $_GET['kelas'] ?? '';

        $conditions = 'm.is_active = 1';
        $params = [];

        if (!empty($search)) {
            $conditions .= ' AND (m.nama LIKE :search OR m.nis LIKE :search)';
            $params['search'] = "%{$search}%";
        }

        if (!empty($kelasFilter)) {
            $conditions .= ' AND m.kelas_id = :kelas_filter';
            $params['kelas_filter'] = $kelasFilter;
        }

        $sql = "SELECT m.*, k.nama_kelas, k.tingkat, k.jurusan
                FROM murid m
                JOIN kelas k ON m.kelas_id = k.id
                WHERE {$conditions}
                ORDER BY k.nama_kelas, m.nama";

        $totalSql = "SELECT COUNT(*) as total FROM murid m JOIN kelas k ON m.kelas_id = k.id WHERE {$conditions}";
        $total = $this->db->fetch($totalSql, $params)['total'];

        $offset = ($page - 1) * RECORDS_PER_PAGE;
        $murid = $this->db->fetchAll($sql . " LIMIT " . RECORDS_PER_PAGE . " OFFSET {$offset}", $params);

        $pagination = [
            'current_page' => $page,
            'total' => $total,
            'per_page' => RECORDS_PER_PAGE,
            'last_page' => ceil($total / RECORDS_PER_PAGE)
        ];

        // Get kelas list for filter
        $kelasList = $this->kelasModel->all('is_active = 1');

        $this->view('admin/murid/index', [
            'murid' => $murid,
            'pagination' => $pagination,
            'search' => $search,
            'kelasFilter' => $kelasFilter,
            'kelasList' => $kelasList
        ]);
    }

    public function addMurid()
    {
        $error = '';
        $success = '';

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            try {
                $this->validateCSRF();

                $data = [
                    'nis' => $this->sanitize($_POST['nis']),
                    'nama' => $this->sanitize($_POST['nama']),
                    'kelas_id' => $_POST['kelas_id'],
                    'jenis_kelamin' => $_POST['jenis_kelamin'],
                    'tempat_lahir' => $this->sanitize($_POST['tempat_lahir']),
                    'tanggal_lahir' => $_POST['tanggal_lahir'],
                    'alamat' => $this->sanitize($_POST['alamat']),
                    'no_hp_ortu' => $this->sanitize($_POST['no_hp_ortu']),
                    'nama_ortu' => $this->sanitize($_POST['nama_ortu'])
                ];

                // Validation
                $errors = $this->validate($data, [
                    'nis' => 'required',
                    'nama' => 'required|min:3',
                    'kelas_id' => 'required|numeric',
                    'jenis_kelamin' => 'required'
                ]);

                if (empty($errors)) {
                    // Check if NIS already exists
                    if ($this->muridModel->isNisExists($data['nis'])) {
                        $errors['nis'] = 'NIS sudah digunakan';
                    }

                    if (empty($errors)) {
                        $this->muridModel->create($data);
                        $success = 'Data murid berhasil ditambahkan';

                        // Reset form
                        $_POST = [];
                    }
                }

                if (!empty($errors)) {
                    $error = implode(', ', $errors);
                }

            } catch (Exception $e) {
                $error = 'Terjadi kesalahan: ' . $e->getMessage();
            }
        }

        // Get kelas list
        $kelasList = $this->kelasModel->all('is_active = 1');

        $this->view('admin/murid/add', [
            'error' => $error,
            'success' => $success,
            'kelasList' => $kelasList,
            'csrf_token' => $this->generateCSRF()
        ]);
    }

    public function editMurid($id)
    {
        $murid = $this->muridModel->getMuridWithKelas($id);
        if (!$murid) {
            $this->redirect('admin/murid');
        }

        $error = '';
        $success = '';

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            try {
                $this->validateCSRF();

                $data = [
                    'nis' => $this->sanitize($_POST['nis']),
                    'nama' => $this->sanitize($_POST['nama']),
                    'kelas_id' => $_POST['kelas_id'],
                    'jenis_kelamin' => $_POST['jenis_kelamin'],
                    'tempat_lahir' => $this->sanitize($_POST['tempat_lahir']),
                    'tanggal_lahir' => $_POST['tanggal_lahir'],
                    'alamat' => $this->sanitize($_POST['alamat']),
                    'no_hp_ortu' => $this->sanitize($_POST['no_hp_ortu']),
                    'nama_ortu' => $this->sanitize($_POST['nama_ortu'])
                ];

                // Validation
                $errors = $this->validate($data, [
                    'nis' => 'required',
                    'nama' => 'required|min:3',
                    'kelas_id' => 'required|numeric',
                    'jenis_kelamin' => 'required'
                ]);

                if (empty($errors)) {
                    // Check if NIS already exists
                    if ($this->muridModel->isNisExists($data['nis'], $id)) {
                        $errors['nis'] = 'NIS sudah digunakan';
                    }

                    if (empty($errors)) {
                        $this->muridModel->update($id, $data);
                        $success = 'Data murid berhasil diperbarui';

                        // Refresh data
                        $murid = $this->muridModel->getMuridWithKelas($id);
                    }
                }

                if (!empty($errors)) {
                    $error = implode(', ', $errors);
                }

            } catch (Exception $e) {
                $error = 'Terjadi kesalahan: ' . $e->getMessage();
            }
        }

        // Get kelas list
        $kelasList = $this->kelasModel->all('is_active = 1');

        $this->view('admin/murid/edit', [
            'murid' => $murid,
            'error' => $error,
            'success' => $success,
            'kelasList' => $kelasList,
            'csrf_token' => $this->generateCSRF()
        ]);
    }

    public function deleteMurid($id)
    {
        try {
            $this->muridModel->softDelete($id);
        } catch (Exception $e) {
            // Handle error silently for now
        }

        $this->redirect('admin/murid');
    }

    // IMPORT EXCEL
    public function importMurid()
    {
        $error = '';
        $success = '';
        $preview = [];
        $step = $_GET['step'] ?? 'upload';

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            try {
                $this->validateCSRF();

                if ($step === 'upload' && isset($_FILES['excel_file'])) {
                    // Handle file upload
                    $file = $_FILES['excel_file'];

                    if ($file['error'] !== UPLOAD_ERR_OK) {
                        throw new Exception('Error uploading file');
                    }

                    $allowedTypes = ['application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'];
                    if (!in_array($file['type'], $allowedTypes)) {
                        throw new Exception('File harus berformat Excel (.xls atau .xlsx)');
                    }

                    if ($file['size'] > MAX_FILE_SIZE) {
                        throw new Exception('Ukuran file terlalu besar. Maksimal ' . (MAX_FILE_SIZE / 1024 / 1024) . 'MB');
                    }

                    // Move uploaded file
                    $fileName = 'import_' . time() . '_' . $file['name'];
                    $filePath = UPLOAD_PATH . $fileName;

                    if (!move_uploaded_file($file['tmp_name'], $filePath)) {
                        throw new Exception('Gagal menyimpan file');
                    }

                    // Read Excel file
                    require_once 'vendor/autoload.php';

                    $reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader('Xlsx');
                    $reader->setReadDataOnly(true);
                    $spreadsheet = $reader->load($filePath);
                    $worksheet = $spreadsheet->getActiveSheet();
                    $data = $worksheet->toArray();

                    // Remove header row
                    array_shift($data);

                    // Process data for preview
                    $kelasList = $this->kelasModel->all('is_active = 1');
                    $kelasMap = [];
                    foreach ($kelasList as $kelas) {
                        $kelasMap[$kelas['id']] = $kelas['nama_kelas'];
                    }

                    foreach ($data as $index => $row) {
                        if (empty($row[0]) && empty($row[1])) continue; // Skip empty rows

                        $preview[] = [
                            'row' => $index + 2, // +2 because we removed header and array is 0-indexed
                            'nis' => $row[0] ?? '',
                            'nama' => $row[1] ?? '',
                            'kelas_id' => $row[2] ?? '',
                            'kelas_nama' => $kelasMap[$row[2]] ?? 'Kelas tidak ditemukan',
                            'jenis_kelamin' => $row[3] ?? '',
                            'tempat_lahir' => $row[4] ?? '',
                            'tanggal_lahir' => $row[5] ?? '',
                            'alamat' => $row[6] ?? '',
                            'no_hp_ortu' => $row[7] ?? '',
                            'nama_ortu' => $row[8] ?? '',
                            'valid' => !empty($row[0]) && !empty($row[1]) && !empty($row[2]) && in_array($row[3], ['L', 'P'])
                        ];
                    }

                    // Store file path in session for processing
                    $_SESSION['import_file'] = $filePath;
                    $_SESSION['import_preview'] = $preview;

                    $step = 'preview';

                } elseif ($step === 'process' && isset($_SESSION['import_preview'])) {
                    // Process the import
                    $preview = $_SESSION['import_preview'];
                    $importData = [];

                    foreach ($preview as $row) {
                        if ($row['valid']) {
                            $importData[] = [
                                'nis' => $row['nis'],
                                'nama' => $row['nama'],
                                'kelas_id' => $row['kelas_id'],
                                'jenis_kelamin' => $row['jenis_kelamin'],
                                'tempat_lahir' => $row['tempat_lahir'],
                                'tanggal_lahir' => $row['tanggal_lahir'] ?: null,
                                'alamat' => $row['alamat'],
                                'no_hp_ortu' => $row['no_hp_ortu'],
                                'nama_ortu' => $row['nama_ortu']
                            ];
                        }
                    }

                    if (!empty($importData)) {
                        $result = $this->muridModel->importFromArray($importData);

                        if ($result['success']) {
                            $success = "Berhasil mengimport {$result['imported']} data murid";

                            // Clean up
                            if (isset($_SESSION['import_file']) && file_exists($_SESSION['import_file'])) {
                                unlink($_SESSION['import_file']);
                            }
                            unset($_SESSION['import_file'], $_SESSION['import_preview']);

                            $step = 'upload';
                            $preview = [];
                        } else {
                            $error = "Import gagal: " . implode(', ', $result['errors']);
                        }
                    } else {
                        $error = 'Tidak ada data valid untuk diimport';
                    }
                }

            } catch (Exception $e) {
                $error = 'Terjadi kesalahan: ' . $e->getMessage();
            }
        }

        // Get preview from session if exists
        if ($step === 'preview' && isset($_SESSION['import_preview'])) {
            $preview = $_SESSION['import_preview'];
        }

        $this->view('admin/murid/import', [
            'error' => $error,
            'success' => $success,
            'step' => $step,
            'preview' => $preview,
            'csrf_token' => $this->generateCSRF()
        ]);
    }
}
