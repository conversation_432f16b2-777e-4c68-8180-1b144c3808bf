/* Custom CSS untuk Sistem Absensi Siswa */

:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --info-color: #0dcaf0;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #212529;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

/* Login Page */
.min-vh-100 {
    min-height: 100vh;
}

.card {
    border: none;
    border-radius: 15px;
}

.card-body {
    padding: 2rem;
}

/* Navigation */
.navbar-brand {
    font-weight: bold;
    font-size: 1.25rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 5px;
}

/* Dashboard Cards */
.card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* Stats Cards */
.card.bg-primary,
.card.bg-success,
.card.bg-info,
.card.bg-warning,
.card.bg-danger {
    border: none;
    background: linear-gradient(135deg, var(--primary-color), #0056b3) !important;
}

.card.bg-success {
    background: linear-gradient(135deg, var(--success-color), #146c43) !important;
}

.card.bg-info {
    background: linear-gradient(135deg, var(--info-color), #0aa2c0) !important;
}

.card.bg-warning {
    background: linear-gradient(135deg, var(--warning-color), #cc9a06) !important;
}

.card.bg-danger {
    background: linear-gradient(135deg, var(--danger-color), #b02a37) !important;
}

/* Tables */
.table {
    background-color: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.table thead th {
    background-color: var(--primary-color);
    color: white;
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.875rem;
    letter-spacing: 0.5px;
}

.table tbody tr {
    transition: background-color 0.2s ease;
}

.table tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.05);
}

/* Buttons */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

/* Forms */
.form-control,
.form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.form-control:focus,
.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.input-group-text {
    border-radius: 8px 0 0 8px;
    border: 2px solid #e9ecef;
    background-color: #f8f9fa;
}

/* Alerts */
.alert {
    border: none;
    border-radius: 10px;
    font-weight: 500;
}

.alert-dismissible .btn-close {
    padding: 1rem 1rem;
}

/* Badges */
.badge {
    font-weight: 500;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
}

/* Pagination */
.pagination .page-link {
    border-radius: 8px;
    margin: 0 2px;
    border: 2px solid #e9ecef;
    color: var(--primary-color);
    font-weight: 500;
}

.pagination .page-link:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Modal */
.modal-content {
    border: none;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.modal-header {
    border-bottom: 2px solid #e9ecef;
    background-color: #f8f9fa;
    border-radius: 15px 15px 0 0;
}

.modal-footer {
    border-top: 2px solid #e9ecef;
    background-color: #f8f9fa;
    border-radius: 0 0 15px 15px;
}

/* Loading Spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Custom Utilities */
.text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.shadow-sm {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.shadow {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1) !important;
}

/* Responsive */
@media (max-width: 768px) {
    .card-body {
        padding: 1.5rem;
    }
    
    .table-responsive {
        border-radius: 10px;
    }
    
    .btn {
        font-size: 0.875rem;
    }
}

/* Print Styles */
@media print {
    .navbar,
    .btn,
    .pagination,
    .modal,
    .alert {
        display: none !important;
    }
    
    .card {
        border: 1px solid #dee2e6 !important;
        box-shadow: none !important;
    }
    
    .table {
        box-shadow: none !important;
    }
}
