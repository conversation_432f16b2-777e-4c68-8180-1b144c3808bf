<?php
$title = 'Import Data Murid - ' . APP_NAME;
ob_start();
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">Import Data Murid</h1>
                    <p class="text-muted">Import data murid dari file Excel (.xls/.xlsx)</p>
                </div>
                <a href="<?= APP_URL ?>/admin/murid" class="btn btn-secondary">
                    <i class="bi bi-arrow-left"></i> Kembali
                </a>
            </div>
        </div>
    </div>
    
    <!-- Progress Steps -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-center">
                <div class="d-flex align-items-center">
                    <div class="step-item <?= $step === 'upload' ? 'active' : ($step === 'preview' || $step === 'process' ? 'completed' : '') ?>">
                        <div class="step-circle">
                            <i class="bi bi-upload"></i>
                        </div>
                        <span class="step-label">Upload File</span>
                    </div>
                    <div class="step-line"></div>
                    <div class="step-item <?= $step === 'preview' ? 'active' : ($step === 'process' ? 'completed' : '') ?>">
                        <div class="step-circle">
                            <i class="bi bi-eye"></i>
                        </div>
                        <span class="step-label">Preview Data</span>
                    </div>
                    <div class="step-line"></div>
                    <div class="step-item <?= $step === 'process' ? 'active' : '' ?>">
                        <div class="step-circle">
                            <i class="bi bi-check-circle"></i>
                        </div>
                        <span class="step-label">Import Selesai</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <?php if (!empty($error)): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="bi bi-exclamation-circle"></i>
            <?= htmlspecialchars($error) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>
    
    <?php if (!empty($success)): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="bi bi-check-circle"></i>
            <?= htmlspecialchars($success) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>
    
    <?php if ($step === 'upload'): ?>
        <!-- Upload Step -->
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="bi bi-upload"></i> Upload File Excel
                        </h5>
                        
                        <div class="alert alert-info">
                            <h6><i class="bi bi-info-circle"></i> Format File Excel:</h6>
                            <div class="table-responsive">
                                <table class="table table-sm table-bordered">
                                    <thead>
                                        <tr>
                                            <th>Kolom A</th>
                                            <th>Kolom B</th>
                                            <th>Kolom C</th>
                                            <th>Kolom D</th>
                                            <th>Kolom E</th>
                                            <th>Kolom F</th>
                                            <th>Kolom G</th>
                                            <th>Kolom H</th>
                                            <th>Kolom I</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>NIS</td>
                                            <td>Nama</td>
                                            <td>ID Kelas</td>
                                            <td>Jenis Kelamin (L/P)</td>
                                            <td>Tempat Lahir</td>
                                            <td>Tanggal Lahir</td>
                                            <td>Alamat</td>
                                            <td>No HP Ortu</td>
                                            <td>Nama Ortu</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <small class="text-muted">
                                <strong>Catatan:</strong> Baris pertama akan diabaikan (header). 
                                Kolom NIS, Nama, ID Kelas, dan Jenis Kelamin wajib diisi.
                            </small>
                        </div>
                        
                        <form method="POST" action="<?= APP_URL ?>/admin/murid/import?step=upload" 
                              enctype="multipart/form-data" class="needs-validation" novalidate>
                            <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">
                            
                            <div class="mb-4">
                                <label for="excel_file" class="form-label">Pilih File Excel <span class="text-danger">*</span></label>
                                <input type="file" class="form-control" id="excel_file" name="excel_file" 
                                       accept=".xls,.xlsx" required>
                                <div class="form-text">
                                    Format yang didukung: .xls, .xlsx (Maksimal <?= MAX_FILE_SIZE / 1024 / 1024 ?>MB)
                                </div>
                                <div class="invalid-feedback">File Excel wajib dipilih</div>
                            </div>
                            
                            <div class="d-flex justify-content-between">
                                <a href="<?= APP_URL ?>/admin/murid" class="btn btn-secondary">
                                    <i class="bi bi-arrow-left"></i> Batal
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-upload"></i> Upload & Preview
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        
    <?php elseif ($step === 'preview' && !empty($preview)): ?>
        <!-- Preview Step -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="bi bi-eye"></i> Preview Data Import
                        </h5>
                        
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle"></i>
                            <strong>Periksa data sebelum import!</strong><br>
                            Data yang ditandai merah akan diabaikan karena tidak valid.
                            Hanya data yang valid (ditandai hijau) yang akan diimport.
                        </div>
                        
                        <div class="table-responsive">
                            <table class="table table-sm table-hover">
                                <thead>
                                    <tr>
                                        <th>Baris</th>
                                        <th>NIS</th>
                                        <th>Nama</th>
                                        <th>Kelas</th>
                                        <th>Jenis Kelamin</th>
                                        <th>Tempat Lahir</th>
                                        <th>Tanggal Lahir</th>
                                        <th>Alamat</th>
                                        <th>No HP Ortu</th>
                                        <th>Nama Ortu</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php 
                                    $validCount = 0;
                                    $invalidCount = 0;
                                    foreach ($preview as $row): 
                                        if ($row['valid']) $validCount++;
                                        else $invalidCount++;
                                    ?>
                                        <tr class="<?= $row['valid'] ? 'table-success' : 'table-danger' ?>">
                                            <td><?= $row['row'] ?></td>
                                            <td><?= htmlspecialchars($row['nis']) ?></td>
                                            <td><?= htmlspecialchars($row['nama']) ?></td>
                                            <td><?= htmlspecialchars($row['kelas_nama']) ?></td>
                                            <td><?= htmlspecialchars($row['jenis_kelamin']) ?></td>
                                            <td><?= htmlspecialchars($row['tempat_lahir']) ?></td>
                                            <td><?= htmlspecialchars($row['tanggal_lahir']) ?></td>
                                            <td><?= htmlspecialchars(substr($row['alamat'], 0, 30)) ?><?= strlen($row['alamat']) > 30 ? '...' : '' ?></td>
                                            <td><?= htmlspecialchars($row['no_hp_ortu']) ?></td>
                                            <td><?= htmlspecialchars($row['nama_ortu']) ?></td>
                                            <td>
                                                <?php if ($row['valid']): ?>
                                                    <span class="badge bg-success">Valid</span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">Invalid</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="row mt-4">
                            <div class="col-md-6">
                                <div class="alert alert-info">
                                    <strong>Ringkasan:</strong><br>
                                    <i class="bi bi-check-circle text-success"></i> Data Valid: <?= $validCount ?><br>
                                    <i class="bi bi-x-circle text-danger"></i> Data Invalid: <?= $invalidCount ?>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="<?= APP_URL ?>/admin/murid/import" class="btn btn-secondary">
                                <i class="bi bi-arrow-left"></i> Upload Ulang
                            </a>
                            <?php if ($validCount > 0): ?>
                                <form method="POST" action="<?= APP_URL ?>/admin/murid/import?step=process" class="d-inline">
                                    <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">
                                    <button type="submit" class="btn btn-success">
                                        <i class="bi bi-download"></i> Import <?= $validCount ?> Data
                                    </button>
                                </form>
                            <?php else: ?>
                                <button class="btn btn-success" disabled>
                                    <i class="bi bi-download"></i> Tidak Ada Data Valid
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<style>
.step-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
}

.step-circle {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: #e9ecef;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    margin-bottom: 8px;
    transition: all 0.3s ease;
}

.step-item.active .step-circle {
    background-color: #0d6efd;
    color: white;
}

.step-item.completed .step-circle {
    background-color: #198754;
    color: white;
}

.step-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #6c757d;
}

.step-item.active .step-label,
.step-item.completed .step-label {
    color: #212529;
}

.step-line {
    width: 100px;
    height: 2px;
    background-color: #e9ecef;
    margin: 0 20px;
    margin-top: 25px;
}
</style>

<?php
$content = ob_get_clean();
include VIEWS_PATH . '/layouts/main.php';
?>
