<?php

class Model
{
    protected $db;
    protected $table;
    protected $primaryKey = 'id';
    
    public function __construct()
    {
        $this->db = Database::getInstance();
    }
    
    public function find($id)
    {
        $sql = "SELECT * FROM {$this->table} WHERE {$this->primaryKey} = :id";
        return $this->db->fetch($sql, ['id' => $id]);
    }
    
    public function findBy($column, $value)
    {
        $sql = "SELECT * FROM {$this->table} WHERE {$column} = :value";
        return $this->db->fetch($sql, ['value' => $value]);
    }
    
    public function all($conditions = '', $params = [])
    {
        $sql = "SELECT * FROM {$this->table}";
        if (!empty($conditions)) {
            $sql .= " WHERE {$conditions}";
        }
        $sql .= " ORDER BY {$this->primaryKey} DESC";
        
        return $this->db->fetchAll($sql, $params);
    }
    
    public function paginate($page = 1, $perPage = RECORDS_PER_PAGE, $conditions = '', $params = [])
    {
        $offset = ($page - 1) * $perPage;
        
        // Get total count
        $countSql = "SELECT COUNT(*) as total FROM {$this->table}";
        if (!empty($conditions)) {
            $countSql .= " WHERE {$conditions}";
        }
        $totalResult = $this->db->fetch($countSql, $params);
        $total = $totalResult['total'];
        
        // Get data
        $sql = "SELECT * FROM {$this->table}";
        if (!empty($conditions)) {
            $sql .= " WHERE {$conditions}";
        }
        $sql .= " ORDER BY {$this->primaryKey} DESC LIMIT {$perPage} OFFSET {$offset}";
        
        $data = $this->db->fetchAll($sql, $params);
        
        return [
            'data' => $data,
            'total' => $total,
            'per_page' => $perPage,
            'current_page' => $page,
            'last_page' => ceil($total / $perPage),
            'from' => $offset + 1,
            'to' => min($offset + $perPage, $total)
        ];
    }
    
    public function create($data)
    {
        // Add timestamps
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        return $this->db->insert($this->table, $data);
    }
    
    public function update($id, $data)
    {
        // Add updated timestamp
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        return $this->db->update($this->table, $data, "{$this->primaryKey} = :id", ['id' => $id]);
    }
    
    public function delete($id)
    {
        return $this->db->delete($this->table, "{$this->primaryKey} = :id", ['id' => $id]);
    }
    
    public function softDelete($id)
    {
        return $this->update($id, ['is_active' => 0]);
    }
    
    public function exists($column, $value, $excludeId = null)
    {
        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE {$column} = :value";
        $params = ['value' => $value];
        
        if ($excludeId) {
            $sql .= " AND {$this->primaryKey} != :exclude_id";
            $params['exclude_id'] = $excludeId;
        }
        
        $result = $this->db->fetch($sql, $params);
        return $result['count'] > 0;
    }
    
    public function count($conditions = '', $params = [])
    {
        $sql = "SELECT COUNT(*) as count FROM {$this->table}";
        if (!empty($conditions)) {
            $sql .= " WHERE {$conditions}";
        }
        
        $result = $this->db->fetch($sql, $params);
        return $result['count'];
    }
    
    public function query($sql, $params = [])
    {
        return $this->db->query($sql, $params);
    }
    
    public function fetch($sql, $params = [])
    {
        return $this->db->fetch($sql, $params);
    }
    
    public function fetchAll($sql, $params = [])
    {
        return $this->db->fetchAll($sql, $params);
    }
}
