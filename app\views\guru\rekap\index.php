<?php
$title = '<PERSON><PERSON><PERSON>las ' . $kelas['nama_kelas'] . ' - ' . APP_NAME;
ob_start();
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="bi bi-file-earmark-text"></i>
                        Rekap Kelas <?= htmlspecialchars($kelas['nama_kelas']) ?>
                    </h1>
                    <p class="text-muted">
                        <?= htmlspecialchars($kelas['mata_pelajaran']) ?> • 
                        Tingkat <?= htmlspecialchars($kelas['tingkat']) ?> • 
                        <?= htmlspecialchars($kelas['jurusan']) ?>
                    </p>
                </div>
                <div>
                    <a href="<?= APP_URL ?>/guru/murid/<?= $kelas['id'] ?>" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i> Kembali
                    </a>
                    <div class="btn-group ms-2">
                        <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="bi bi-download"></i> Export
                        </button>
                        <ul class="dropdown-menu">
                            <li>
                                <a class="dropdown-item" href="<?= APP_URL ?>/guru/export/pdf/<?= $kelas['id'] ?>">
                                    <i class="bi bi-file-earmark-pdf text-danger"></i> Export PDF
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="<?= APP_URL ?>/guru/export/excel/<?= $kelas['id'] ?>">
                                    <i class="bi bi-file-earmark-excel text-success"></i> Export Excel
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title"><?= count($rekap) ?></h4>
                            <p class="card-text">Total Murid</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-people" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">
                                <?= count(array_filter($rekap, function($m) { return $m['total_nilai'] > 0; })) ?>
                            </h4>
                            <p class="card-text">Sudah Dinilai</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-check-circle" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">
                                <?php 
                                $totalNilai = array_sum(array_column($rekap, 'total_nilai'));
                                echo $totalNilai;
                                ?>
                            </h4>
                            <p class="card-text">Total Nilai</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-bar-chart" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">
                                <?php 
                                $rataKelas = count($rekap) > 0 ? 
                                    round(array_sum(array_filter(array_column($rekap, 'rata_nilai'))) / count(array_filter(array_column($rekap, 'rata_nilai'))), 1) : 0;
                                echo $rataKelas ?: '-';
                                ?>
                            </h4>
                            <p class="card-text">Rata-rata Kelas</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-trophy" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Rekap Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-table"></i>
                        Rekap Nilai dan Catatan
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($rekap)): ?>
                        <div class="table-responsive">
                            <table class="table table-hover" id="rekapTable">
                                <thead>
                                    <tr>
                                        <th>No</th>
                                        <th>NIS</th>
                                        <th>Nama</th>
                                        <th>JK</th>
                                        <th>Total Nilai</th>
                                        <th>Rata-rata</th>
                                        <th>Nilai Terakhir</th>
                                        <th>Total Catatan</th>
                                        <th>Total Absensi</th>
                                        <th>Update Terakhir</th>
                                        <th>Aksi</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php 
                                    $no = 1;
                                    foreach ($rekap as $murid): 
                                    ?>
                                        <tr>
                                            <td><?= $no++ ?></td>
                                            <td>
                                                <strong><?= htmlspecialchars($murid['nis']) ?></strong>
                                            </td>
                                            <td>
                                                <strong><?= htmlspecialchars($murid['nama']) ?></strong>
                                            </td>
                                            <td>
                                                <span class="badge <?= $murid['jenis_kelamin'] === 'L' ? 'bg-primary' : 'bg-pink' ?>">
                                                    <?= $murid['jenis_kelamin'] ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">
                                                    <?= $murid['total_nilai'] ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if ($murid['rata_nilai']): ?>
                                                    <span class="badge <?= $murid['rata_nilai'] >= 75 ? 'bg-success' : ($murid['rata_nilai'] >= 60 ? 'bg-warning' : 'bg-danger') ?>">
                                                        <?= round($murid['rata_nilai'], 1) ?>
                                                    </span>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if (!empty($murid['nilai_detail'])): ?>
                                                    <div class="d-flex gap-1">
                                                        <?php foreach (array_slice($murid['nilai_detail'], 0, 3) as $nilai): ?>
                                                            <span class="badge bg-secondary" data-bs-toggle="tooltip" 
                                                                  title="<?= date('d/m/Y', strtotime($nilai['tanggal'])) ?>">
                                                                <?= $nilai['nilai'] ?>
                                                            </span>
                                                        <?php endforeach; ?>
                                                        <?php if (count($murid['nilai_detail']) > 3): ?>
                                                            <span class="text-muted">...</span>
                                                        <?php endif; ?>
                                                    </div>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-warning">
                                                    <?= $murid['total_catatan'] ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary">
                                                    <?= $murid['total_absensi'] ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if ($murid['last_update']): ?>
                                                    <small class="text-muted">
                                                        <?= date('d/m/Y', strtotime($murid['last_update'])) ?>
                                                    </small>
                                                <?php else: ?>
                                                    <small class="text-muted">Belum ada</small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <a href="<?= APP_URL ?>/guru/nilai/<?= $murid['id'] ?>" 
                                                   class="btn btn-sm btn-primary" 
                                                   data-bs-toggle="tooltip" title="Input Nilai/Catatan">
                                                    <i class="bi bi-pencil-square"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- Print Button -->
                        <div class="mt-3 text-center">
                            <button onclick="printTable()" class="btn btn-outline-secondary">
                                <i class="bi bi-printer"></i> Print Tabel
                            </button>
                        </div>
                        
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="bi bi-file-earmark-text text-muted" style="font-size: 4rem;"></i>
                            <h5 class="mt-3 text-muted">Belum Ada Data</h5>
                            <p class="text-muted">Belum ada data nilai atau catatan untuk kelas ini</p>
                            <a href="<?= APP_URL ?>/guru/murid/<?= $kelas['id'] ?>" class="btn btn-primary">
                                <i class="bi bi-plus-circle"></i> Mulai Input Nilai
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.bg-pink {
    background-color: #e91e63 !important;
}

@media print {
    .btn, .card-header, .navbar {
        display: none !important;
    }
    .card {
        border: none !important;
        box-shadow: none !important;
    }
}
</style>

<script>
function printTable() {
    window.print();
}

// Initialize tooltips
document.addEventListener('DOMContentLoaded', function() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>

<?php
$content = ob_get_clean();
include VIEWS_PATH . '/layouts/main.php';
?>
