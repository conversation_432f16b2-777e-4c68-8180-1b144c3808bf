<?php

class AuthController extends Controller
{
    private $userModel;
    
    public function __construct()
    {
        parent::__construct();
        $this->userModel = new User();
    }
    
    public function login()
    {
        // Redirect if already logged in
        if ($this->isLoggedIn()) {
            $this->redirect('dashboard');
        }
        
        $error = '';
        $timeout = isset($_GET['timeout']) ? 'Session expired. Please login again.' : '';
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            try {
                $this->validateCSRF();
                
                $username = $this->sanitize($_POST['username'] ?? '');
                $password = $_POST['password'] ?? '';
                
                // Validation
                $errors = $this->validate([
                    'username' => $username,
                    'password' => $password
                ], [
                    'username' => 'required',
                    'password' => 'required'
                ]);
                
                if (empty($errors)) {
                    $user = $this->userModel->authenticate($username, $password);
                    
                    if ($user) {
                        // Set session
                        $_SESSION['user_id'] = $user['id'];
                        $_SESSION['username'] = $user['username'];
                        $_SESSION['role'] = $user['role'];
                        $_SESSION['nama'] = $user['nama'];
                        $_SESSION['last_activity'] = time();
                        
                        // Get additional user info if guru
                        if ($user['role'] === 'guru') {
                            $userWithRole = $this->userModel->getUserWithRole($user['id']);
                            if ($userWithRole) {
                                $_SESSION['guru_id'] = $userWithRole['guru_id'];
                            }
                        }
                        
                        $this->redirect('dashboard');
                    } else {
                        $error = 'Invalid username or password';
                    }
                } else {
                    $error = implode(', ', $errors);
                }
            } catch (Exception $e) {
                $error = 'An error occurred. Please try again.';
            }
        }
        
        $this->view('auth/login', [
            'error' => $error,
            'timeout' => $timeout,
            'csrf_token' => $this->generateCSRF()
        ]);
    }
    
    public function logout()
    {
        session_destroy();
        $this->redirect('login');
    }
}
