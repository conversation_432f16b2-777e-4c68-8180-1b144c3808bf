<?php
$title = 'Rekap Data Sekolah - ' . APP_NAME;
ob_start();
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="bi bi-file-earmark-text"></i>
                        Rekap Data Sekolah
                    </h1>
                    <p class="text-muted">Laporan dan statistik data sekolah</p>
                </div>
                <div class="btn-group">
                    <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown">
                        <i class="bi bi-download"></i> Export Data
                    </button>
                    <ul class="dropdown-menu">
                        <li>
                            <a class="dropdown-item" href="<?= APP_URL ?>/admin/export/pdf<?= !empty($kelasFilter) ? '?kelas=' . $kelasFilter : '' ?>">
                                <i class="bi bi-file-earmark-pdf text-danger"></i> Export PDF
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="<?= APP_URL ?>/admin/export/excel<?= !empty($kelasFilter) ? '?kelas=' . $kelasFilter : '' ?>">
                                <i class="bi bi-file-earmark-excel text-success"></i> Export Excel
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title"><?= $stats['total_guru'] ?></h4>
                            <p class="card-text">Total Guru</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-person-badge" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title"><?= $stats['total_kelas'] ?></h4>
                            <p class="card-text">Total Kelas</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-building" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title"><?= $stats['total_murid'] ?></h4>
                            <p class="card-text">Total Murid</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-people" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title"><?= $stats['total_nilai'] ?></h4>
                            <p class="card-text">Total Nilai</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-bar-chart" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Filter and Search -->
    <div class="row mb-4">
        <div class="col-md-6">
            <form method="GET" action="<?= APP_URL ?>/admin/rekap">
                <div class="input-group">
                    <input type="text" class="form-control" name="search" 
                           placeholder="Cari nama kelas..." 
                           value="<?= htmlspecialchars($search) ?>">
                    <select class="form-select" name="kelas" style="max-width: 200px;">
                        <option value="">Semua Kelas</option>
                        <?php foreach ($kelasList as $kelas): ?>
                            <option value="<?= $kelas['id'] ?>" <?= $kelasFilter == $kelas['id'] ? 'selected' : '' ?>>
                                <?= htmlspecialchars($kelas['nama_kelas']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <button class="btn btn-outline-secondary" type="submit">
                        <i class="bi bi-search"></i>
                    </button>
                    <?php if (!empty($search) || !empty($kelasFilter)): ?>
                        <a href="<?= APP_URL ?>/admin/rekap" class="btn btn-outline-danger">
                            <i class="bi bi-x"></i>
                        </a>
                    <?php endif; ?>
                </div>
            </form>
        </div>
        <div class="col-md-6 text-end">
            <small class="text-muted">
                Total: <?= count($rekapKelas) ?> kelas
            </small>
        </div>
    </div>
    
    <!-- Rekap Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-table"></i>
                        Rekap Per Kelas
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($rekapKelas)): ?>
                        <div class="table-responsive">
                            <table class="table table-hover" id="rekapTable">
                                <thead>
                                    <tr>
                                        <th>No</th>
                                        <th>Kelas</th>
                                        <th>Tingkat</th>
                                        <th>Jurusan</th>
                                        <th>Tahun Ajaran</th>
                                        <th>Total Murid</th>
                                        <th>Laki-laki</th>
                                        <th>Perempuan</th>
                                        <th>Guru Mengajar</th>
                                        <th>Total Aktivitas</th>
                                        <th>Rata-rata Nilai</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php 
                                    $no = 1;
                                    $totalMurid = 0;
                                    $totalLaki = 0;
                                    $totalPerempuan = 0;
                                    $totalAktivitas = 0;
                                    
                                    foreach ($rekapKelas as $kelas): 
                                        $totalMurid += $kelas['total_murid'];
                                        $totalLaki += $kelas['laki_laki'];
                                        $totalPerempuan += $kelas['perempuan'];
                                        $totalAktivitas += $kelas['total_aktivitas'];
                                    ?>
                                        <tr>
                                            <td><?= $no++ ?></td>
                                            <td>
                                                <strong><?= htmlspecialchars($kelas['nama_kelas']) ?></strong>
                                            </td>
                                            <td>
                                                <span class="badge bg-primary"><?= htmlspecialchars($kelas['tingkat']) ?></span>
                                            </td>
                                            <td><?= htmlspecialchars($kelas['jurusan']) ?></td>
                                            <td><?= htmlspecialchars($kelas['tahun_ajaran']) ?></td>
                                            <td>
                                                <span class="badge bg-info">
                                                    <i class="bi bi-people"></i> <?= $kelas['total_murid'] ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-primary">
                                                    <?= $kelas['laki_laki'] ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-pink">
                                                    <?= $kelas['perempuan'] ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-success">
                                                    <?= $kelas['total_guru_mengajar'] ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-warning">
                                                    <?= $kelas['total_aktivitas'] ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if ($kelas['rata_nilai_kelas']): ?>
                                                    <span class="badge <?= $kelas['rata_nilai_kelas'] >= 75 ? 'bg-success' : ($kelas['rata_nilai_kelas'] >= 60 ? 'bg-warning' : 'bg-danger') ?>">
                                                        <?= round($kelas['rata_nilai_kelas'], 1) ?>
                                                    </span>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                                <tfoot>
                                    <tr class="table-secondary fw-bold">
                                        <td colspan="5" class="text-center">TOTAL</td>
                                        <td>
                                            <span class="badge bg-info">
                                                <i class="bi bi-people"></i> <?= $totalMurid ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-primary"><?= $totalLaki ?></span>
                                        </td>
                                        <td>
                                            <span class="badge bg-pink"><?= $totalPerempuan ?></span>
                                        </td>
                                        <td>-</td>
                                        <td>
                                            <span class="badge bg-warning"><?= $totalAktivitas ?></span>
                                        </td>
                                        <td>-</td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                        
                        <!-- Summary -->
                        <div class="row mt-4">
                            <div class="col-md-12">
                                <div class="alert alert-light">
                                    <h6><i class="bi bi-info-circle"></i> Ringkasan:</h6>
                                    <div class="row text-center">
                                        <div class="col-md-3">
                                            <strong class="text-primary"><?= count($rekapKelas) ?></strong>
                                            <br><small class="text-muted">Total Kelas</small>
                                        </div>
                                        <div class="col-md-3">
                                            <strong class="text-success"><?= $totalMurid ?></strong>
                                            <br><small class="text-muted">Total Murid</small>
                                        </div>
                                        <div class="col-md-3">
                                            <strong class="text-info"><?= $totalLaki ?> : <?= $totalPerempuan ?></strong>
                                            <br><small class="text-muted">Rasio L : P</small>
                                        </div>
                                        <div class="col-md-3">
                                            <strong class="text-warning"><?= $totalAktivitas ?></strong>
                                            <br><small class="text-muted">Total Aktivitas</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Print Button -->
                        <div class="mt-3 text-center">
                            <button onclick="printTable()" class="btn btn-outline-secondary">
                                <i class="bi bi-printer"></i> Print Tabel
                            </button>
                        </div>
                        
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="bi bi-file-earmark-text text-muted" style="font-size: 4rem;"></i>
                            <h5 class="mt-3 text-muted">Belum Ada Data</h5>
                            <p class="text-muted">Belum ada data kelas atau murid untuk ditampilkan</p>
                            <a href="<?= APP_URL ?>/admin/kelas/add" class="btn btn-primary">
                                <i class="bi bi-plus-circle"></i> Tambah Kelas
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.bg-pink {
    background-color: #e91e63 !important;
}

@media print {
    .btn, .card-header, .navbar, .alert {
        display: none !important;
    }
    .card {
        border: none !important;
        box-shadow: none !important;
    }
}
</style>

<script>
function printTable() {
    window.print();
}
</script>

<?php
$content = ob_get_clean();
include VIEWS_PATH . '/layouts/main.php';
?>
