<?php

class Router
{
    private $routes = [];
    
    public function add($route, $action)
    {
        $this->routes[$route] = $action;
    }
    
    public function dispatch($url)
    {
        // Remove trailing slash
        $url = rtrim($url, '/');
        
        // Check for exact match first
        if (isset($this->routes[$url])) {
            $this->callAction($this->routes[$url]);
            return;
        }
        
        // Check for parameterized routes
        foreach ($this->routes as $route => $action) {
            $pattern = $this->convertRouteToRegex($route);
            if (preg_match($pattern, $url, $matches)) {
                array_shift($matches); // Remove full match
                $this->callAction($action, $matches);
                return;
            }
        }
        
        // Route not found
        $this->notFound();
    }
    
    private function convertRouteToRegex($route)
    {
        // Convert {param} to regex capture groups
        $pattern = preg_replace('/\{([^}]+)\}/', '([^/]+)', $route);
        return '#^' . $pattern . '$#';
    }
    
    private function callAction($action, $params = [])
    {
        list($controller, $method) = explode('@', $action);
        
        if (!class_exists($controller)) {
            $this->notFound();
            return;
        }
        
        $controllerInstance = new $controller();
        
        if (!method_exists($controllerInstance, $method)) {
            $this->notFound();
            return;
        }
        
        call_user_func_array([$controllerInstance, $method], $params);
    }
    
    private function notFound()
    {
        http_response_code(404);
        echo "404 - Page Not Found";
    }
}
