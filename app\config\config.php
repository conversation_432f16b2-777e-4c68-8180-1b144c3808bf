<?php
/**
 * Konfigurasi aplikasi Absensi Siswa
 */

// Database configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'absensi_siswa');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// Application configuration
define('APP_NAME', 'Sistem Absensi Siswa');
define('APP_VERSION', '1.0.0');
define('APP_URL', 'http://localhost/Absensi%20siswa%20berbasis%20web');

// Session configuration
define('SESSION_TIMEOUT', 3600); // 1 hour

// File upload configuration
define('UPLOAD_PATH', ROOT_PATH . '/uploads/');
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_EXTENSIONS', ['xls', 'xlsx']);

// Pagination
define('RECORDS_PER_PAGE', 10);

// Date format
define('DATE_FORMAT', 'd/m/Y');
define('DATETIME_FORMAT', 'd/m/Y H:i:s');

// User roles
define('ROLE_ADMIN', 'admin');
define('ROLE_GURU', 'guru');

// Create upload directory if not exists
if (!file_exists(UPLOAD_PATH)) {
    mkdir(UPLOAD_PATH, 0755, true);
}
