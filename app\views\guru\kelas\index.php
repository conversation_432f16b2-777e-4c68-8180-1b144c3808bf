<?php
$title = '<PERSON><PERSON> - ' . APP_NAME;
ob_start();
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0"><PERSON><PERSON> yang Saya Ampu</h1>
                    <p class="text-muted">Daftar kelas yang Anda ajarkan</p>
                </div>
                <div class="text-end">
                    <small class="text-muted">
                        <i class="bi bi-person-badge"></i>
                        <?= htmlspecialchars($_SESSION['nama']) ?>
                    </small>
                </div>
            </div>
        </div>
    </div>
    
    <?php if (!empty($kelasList)): ?>
        <div class="row">
            <?php foreach ($kelasList as $kelas): ?>
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card h-100 shadow-sm">
                        <div class="card-header bg-primary text-white">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-building"></i>
                                <?= htmlspecialchars($kelas['nama_kelas']) ?>
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <div class="row">
                                    <div class="col-6">
                                        <small class="text-muted">Tingkat</small>
                                        <div class="fw-bold"><?= htmlspecialchars($kelas['tingkat']) ?></div>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">Jurusan</small>
                                        <div class="fw-bold"><?= htmlspecialchars($kelas['jurusan']) ?></div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <small class="text-muted">Mata Pelajaran</small>
                                <div class="fw-bold text-success">
                                    <i class="bi bi-book"></i>
                                    <?= htmlspecialchars($kelas['mata_pelajaran']) ?>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <small class="text-muted">Tahun Ajaran</small>
                                <div class="fw-bold"><?= htmlspecialchars($kelas['tahun_ajaran']) ?></div>
                            </div>
                            
                            <div class="alert alert-info mb-3">
                                <i class="bi bi-people"></i>
                                <strong><?= $kelas['total_murid'] ?></strong> murid terdaftar
                            </div>
                        </div>
                        <div class="card-footer bg-light">
                            <div class="d-grid gap-2">
                                <a href="<?= APP_URL ?>/guru/murid/<?= $kelas['id'] ?>" 
                                   class="btn btn-primary">
                                    <i class="bi bi-people"></i> Lihat Murid
                                </a>
                                <div class="btn-group">
                                    <a href="<?= APP_URL ?>/guru/rekap/<?= $kelas['id'] ?>" 
                                       class="btn btn-outline-secondary btn-sm">
                                        <i class="bi bi-file-earmark-text"></i> Rekap
                                    </a>
                                    <a href="<?= APP_URL ?>/guru/export/pdf/<?= $kelas['id'] ?>" 
                                       class="btn btn-outline-danger btn-sm">
                                        <i class="bi bi-file-earmark-pdf"></i> PDF
                                    </a>
                                    <a href="<?= APP_URL ?>/guru/export/excel/<?= $kelas['id'] ?>" 
                                       class="btn btn-outline-success btn-sm">
                                        <i class="bi bi-file-earmark-excel"></i> Excel
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        
        <!-- Summary Statistics -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-bar-chart"></i>
                            Ringkasan
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-3">
                                <div class="border-end">
                                    <h4 class="text-primary"><?= count($kelasList) ?></h4>
                                    <small class="text-muted">Total Kelas</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="border-end">
                                    <h4 class="text-success"><?= array_sum(array_column($kelasList, 'total_murid')) ?></h4>
                                    <small class="text-muted">Total Murid</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="border-end">
                                    <h4 class="text-info"><?= count(array_unique(array_column($kelasList, 'mata_pelajaran'))) ?></h4>
                                    <small class="text-muted">Mata Pelajaran</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <h4 class="text-warning"><?= count(array_unique(array_column($kelasList, 'tahun_ajaran'))) ?></h4>
                                <small class="text-muted">Tahun Ajaran</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
    <?php else: ?>
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center py-5">
                        <i class="bi bi-building text-muted" style="font-size: 4rem;"></i>
                        <h5 class="mt-3 text-muted">Belum Ada Kelas yang Diampu</h5>
                        <p class="text-muted">
                            Anda belum ditugaskan untuk mengajar kelas manapun.<br>
                            Silakan hubungi administrator untuk penugasan kelas.
                        </p>
                        <a href="<?= APP_URL ?>/dashboard" class="btn btn-primary">
                            <i class="bi bi-house"></i> Kembali ke Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<?php
$content = ob_get_clean();
include VIEWS_PATH . '/layouts/main.php';
?>
