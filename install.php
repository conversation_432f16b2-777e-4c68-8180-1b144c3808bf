<?php
/**
 * Installation script untuk Sistem Absensi Siswa
 * Script ini akan membuat database dan tabel yang diperlukan
 */

// Include config
require_once 'app/config/config.php';

echo "<h1>Instalasi Sistem Absensi Siswa</h1>";

try {
    // Connect to MySQL server (without database)
    $dsn = "mysql:host=" . DB_HOST . ";charset=" . DB_CHARSET;
    $pdo = new PDO($dsn, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p>✓ Koneksi ke MySQL server berhasil</p>";
    
    // Create database if not exists
    $pdo->exec("CREATE DATABASE IF NOT EXISTS " . DB_NAME . " CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "<p>✓ Database '" . DB_NAME . "' berhasil dibuat/sudah ada</p>";
    
    // Use the database
    $pdo->exec("USE " . DB_NAME);
    
    // Read and execute schema.sql
    $schema = file_get_contents('database/schema.sql');
    
    // Remove the first few lines that create database and use database
    $lines = explode("\n", $schema);
    $filteredLines = [];
    $skipLines = true;
    
    foreach ($lines as $line) {
        $line = trim($line);
        if (strpos($line, 'USE ') === 0) {
            $skipLines = false;
            continue;
        }
        if (!$skipLines && !empty($line)) {
            $filteredLines[] = $line;
        }
    }
    
    $schema = implode("\n", $filteredLines);
    
    // Split by semicolon and execute each statement
    $statements = explode(';', $schema);
    
    foreach ($statements as $statement) {
        $statement = trim($statement);
        if (!empty($statement)) {
            try {
                $pdo->exec($statement);
            } catch (PDOException $e) {
                // Ignore table already exists errors
                if (strpos($e->getMessage(), 'already exists') === false) {
                    throw $e;
                }
            }
        }
    }
    
    echo "<p>✓ Tabel database berhasil dibuat</p>";
    
    // Check if admin user exists
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE username = 'admin'");
    $stmt->execute();
    $adminExists = $stmt->fetchColumn() > 0;
    
    if (!$adminExists) {
        // Create default admin user
        $adminPassword = password_hash('password', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("INSERT INTO users (username, password, role, nama, email) VALUES (?, ?, 'admin', 'Administrator', '<EMAIL>')");
        $stmt->execute(['admin', $adminPassword]);
        echo "<p>✓ User admin default berhasil dibuat</p>";
    } else {
        echo "<p>✓ User admin sudah ada</p>";
    }
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>✅ Instalasi Berhasil!</h3>";
    echo "<p><strong>Informasi Login:</strong></p>";
    echo "<ul>";
    echo "<li><strong>Admin:</strong> username = <code>admin</code>, password = <code>password</code></li>";
    echo "<li><strong>Guru Demo:</strong> username = <code>guru1</code>, password = <code>password</code></li>";
    echo "</ul>";
    echo "<p><a href='index.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Login ke Sistem</a></p>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>❌ Error Instalasi</h3>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "<p>Pastikan:</p>";
    echo "<ul>";
    echo "<li>MySQL server berjalan</li>";
    echo "<li>Konfigurasi database di app/config/config.php benar</li>";
    echo "<li>User database memiliki permission untuk membuat database</li>";
    echo "</ul>";
    echo "</div>";
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>❌ Error</h3>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "</div>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 50px auto;
    padding: 20px;
    background: #f8f9fa;
}

h1 {
    color: #333;
    text-align: center;
    margin-bottom: 30px;
}

p {
    margin: 10px 0;
    padding: 5px 0;
}

code {
    background: #e9ecef;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: monospace;
}
</style>
