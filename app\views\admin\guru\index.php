<?php
$title = 'Data Guru - ' . APP_NAME;
ob_start();
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">Data Guru</h1>
                    <p class="text-muted">Kelola data guru dan akun login</p>
                </div>
                <a href="<?= APP_URL ?>/admin/guru/add" class="btn btn-primary">
                    <i class="bi bi-plus-circle"></i> <PERSON><PERSON> Guru
                </a>
            </div>
        </div>
    </div>
    
    <!-- Search and Filter -->
    <div class="row mb-4">
        <div class="col-md-6">
            <form method="GET" action="<?= APP_URL ?>/admin/guru">
                <div class="input-group">
                    <input type="text" class="form-control" name="search" 
                           placeholder="<PERSON><PERSON> nama, NIP, atau username..." 
                           value="<?= htmlspecialchars($search) ?>">
                    <button class="btn btn-outline-secondary" type="submit">
                        <i class="bi bi-search"></i>
                    </button>
                    <?php if (!empty($search)): ?>
                        <a href="<?= APP_URL ?>/admin/guru" class="btn btn-outline-danger">
                            <i class="bi bi-x"></i>
                        </a>
                    <?php endif; ?>
                </div>
            </form>
        </div>
        <div class="col-md-6 text-end">
            <small class="text-muted">
                Total: <?= $pagination['total'] ?> guru
            </small>
        </div>
    </div>
    
    <!-- Data Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <?php if (!empty($guru)): ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>No</th>
                                        <th>NIP</th>
                                        <th>Nama</th>
                                        <th>Mata Pelajaran</th>
                                        <th>Username</th>
                                        <th>Email</th>
                                        <th>No. HP</th>
                                        <th>Status</th>
                                        <th>Aksi</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php 
                                    $no = ($pagination['current_page'] - 1) * $pagination['per_page'] + 1;
                                    foreach ($guru as $g): 
                                    ?>
                                        <tr>
                                            <td><?= $no++ ?></td>
                                            <td>
                                                <strong><?= htmlspecialchars($g['nip']) ?></strong>
                                            </td>
                                            <td>
                                                <div>
                                                    <strong><?= htmlspecialchars($g['nama']) ?></strong>
                                                    <?php if (!empty($g['alamat'])): ?>
                                                        <br><small class="text-muted">
                                                            <?= htmlspecialchars(substr($g['alamat'], 0, 50)) ?>
                                                            <?= strlen($g['alamat']) > 50 ? '...' : '' ?>
                                                        </small>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td><?= htmlspecialchars($g['mata_pelajaran']) ?></td>
                                            <td>
                                                <code><?= htmlspecialchars($g['username']) ?></code>
                                            </td>
                                            <td><?= htmlspecialchars($g['email']) ?></td>
                                            <td><?= htmlspecialchars($g['no_hp']) ?></td>
                                            <td>
                                                <?php if ($g['is_active'] && $g['user_active']): ?>
                                                    <span class="badge bg-success">Aktif</span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">Nonaktif</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="<?= APP_URL ?>/admin/guru/edit/<?= $g['id'] ?>" 
                                                       class="btn btn-outline-primary" 
                                                       data-bs-toggle="tooltip" title="Edit">
                                                        <i class="bi bi-pencil"></i>
                                                    </a>
                                                    <a href="<?= APP_URL ?>/admin/guru/delete/<?= $g['id'] ?>" 
                                                       class="btn btn-outline-danger btn-delete" 
                                                       data-bs-toggle="tooltip" title="Hapus"
                                                       data-message="Apakah Anda yakin ingin menghapus guru <?= htmlspecialchars($g['nama']) ?>?">
                                                        <i class="bi bi-trash"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- Pagination -->
                        <?php if ($pagination['last_page'] > 1): ?>
                            <nav aria-label="Page navigation" class="mt-4">
                                <ul class="pagination justify-content-center">
                                    <?php if ($pagination['current_page'] > 1): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="<?= APP_URL ?>/admin/guru?page=<?= $pagination['current_page'] - 1 ?><?= !empty($search) ? '&search=' . urlencode($search) : '' ?>">
                                                <i class="bi bi-chevron-left"></i>
                                            </a>
                                        </li>
                                    <?php endif; ?>
                                    
                                    <?php for ($i = max(1, $pagination['current_page'] - 2); $i <= min($pagination['last_page'], $pagination['current_page'] + 2); $i++): ?>
                                        <li class="page-item <?= $i == $pagination['current_page'] ? 'active' : '' ?>">
                                            <a class="page-link" href="<?= APP_URL ?>/admin/guru?page=<?= $i ?><?= !empty($search) ? '&search=' . urlencode($search) : '' ?>">
                                                <?= $i ?>
                                            </a>
                                        </li>
                                    <?php endfor; ?>
                                    
                                    <?php if ($pagination['current_page'] < $pagination['last_page']): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="<?= APP_URL ?>/admin/guru?page=<?= $pagination['current_page'] + 1 ?><?= !empty($search) ? '&search=' . urlencode($search) : '' ?>">
                                                <i class="bi bi-chevron-right"></i>
                                            </a>
                                        </li>
                                    <?php endif; ?>
                                </ul>
                            </nav>
                        <?php endif; ?>
                        
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="bi bi-person-badge text-muted" style="font-size: 4rem;"></i>
                            <h5 class="mt-3 text-muted">Belum ada data guru</h5>
                            <p class="text-muted">Klik tombol "Tambah Guru" untuk menambahkan data guru baru</p>
                            <a href="<?= APP_URL ?>/admin/guru/add" class="btn btn-primary">
                                <i class="bi bi-plus-circle"></i> Tambah Guru
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$content = ob_get_clean();
include VIEWS_PATH . '/layouts/main.php';
?>
