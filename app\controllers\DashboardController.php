<?php

class DashboardController extends Controller
{
    private $userModel;
    private $guruModel;
    private $kelasModel;
    private $muridModel;
    
    public function __construct()
    {
        parent::__construct();
        $this->requireLogin();
        
        $this->userModel = new User();
        $this->guruModel = new Guru();
        $this->kelasModel = new Kelas();
        $this->muridModel = new Murid();
    }
    
    public function index()
    {
        $user = $this->getCurrentUser();
        $stats = [];
        
        if ($user['role'] === 'admin') {
            $stats = [
                'total_guru' => $this->guruModel->count('is_active = 1'),
                'total_kelas' => $this->kelasModel->count('is_active = 1'),
                'total_murid' => $this->muridModel->count('is_active = 1'),
                'total_users' => $this->userModel->count('is_active = 1')
            ];
            
            // Recent activities
            $recentMurid = $this->muridModel->fetchAll(
                "SELECT m.nama, k.nama_kelas, m.created_at 
                 FROM murid m 
                 JOIN kelas k ON m.kelas_id = k.id 
                 WHERE m.is_active = 1 
                 ORDER BY m.created_at DESC 
                 LIMIT 5"
            );
            
            $stats['recent_murid'] = $recentMurid;
            
        } else if ($user['role'] === 'guru') {
            // Get guru's classes and students
            $guruKelas = $this->guruModel->fetchAll(
                "SELECT k.id, k.nama_kelas, k.tingkat, k.jurusan,
                        COUNT(m.id) as total_murid
                 FROM guru_kelas gk
                 JOIN kelas k ON gk.kelas_id = k.id
                 LEFT JOIN murid m ON k.id = m.kelas_id AND m.is_active = 1
                 WHERE gk.guru_id = :guru_id AND k.is_active = 1
                 GROUP BY k.id
                 ORDER BY k.nama_kelas",
                ['guru_id' => $_SESSION['guru_id']]
            );
            
            $stats = [
                'total_kelas' => count($guruKelas),
                'total_murid' => array_sum(array_column($guruKelas, 'total_murid')),
                'kelas_list' => $guruKelas
            ];
        }
        
        $this->view('dashboard/index', [
            'user' => $user,
            'stats' => $stats
        ]);
    }
}
