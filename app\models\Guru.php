<?php

class Guru extends Model
{
    protected $table = 'guru';
    
    public function getGuruWithUser($id)
    {
        $sql = "SELECT g.*, u.username, u.email, u.is_active as user_active
                FROM guru g
                LEFT JOIN users u ON g.user_id = u.id
                WHERE g.id = :id";
        
        return $this->db->fetch($sql, ['id' => $id]);
    }
    
    public function getAllGuruWithUser()
    {
        $sql = "SELECT g.*, u.username, u.email, u.is_active as user_active
                FROM guru g
                LEFT JOIN users u ON g.user_id = u.id
                WHERE g.is_active = 1
                ORDER BY g.nama";
        
        return $this->db->fetchAll($sql);
    }
    
    public function getGuruKelas($guruId)
    {
        $sql = "SELECT k.id, k.nama_kelas, k.tingkat, k.jurusan, gk.mata_pelajaran
                FROM guru_kelas gk
                JOIN kelas k ON gk.kelas_id = k.id
                WHERE gk.guru_id = :guru_id AND k.is_active = 1
                ORDER BY k.nama_kelas";
        
        return $this->db->fetchAll($sql, ['guru_id' => $guruId]);
    }
    
    public function assignKelas($guruId, $kelasId, $mataPelajaran)
    {
        // Check if already assigned
        $existing = $this->db->fetch(
            "SELECT id FROM guru_kelas WHERE guru_id = :guru_id AND kelas_id = :kelas_id",
            ['guru_id' => $guruId, 'kelas_id' => $kelasId]
        );
        
        if ($existing) {
            // Update existing assignment
            return $this->db->update(
                'guru_kelas',
                ['mata_pelajaran' => $mataPelajaran],
                'guru_id = :guru_id AND kelas_id = :kelas_id',
                ['guru_id' => $guruId, 'kelas_id' => $kelasId]
            );
        } else {
            // Create new assignment
            return $this->db->insert('guru_kelas', [
                'guru_id' => $guruId,
                'kelas_id' => $kelasId,
                'mata_pelajaran' => $mataPelajaran,
                'created_at' => date('Y-m-d H:i:s')
            ]);
        }
    }
    
    public function removeKelas($guruId, $kelasId)
    {
        return $this->db->delete(
            'guru_kelas',
            'guru_id = :guru_id AND kelas_id = :kelas_id',
            ['guru_id' => $guruId, 'kelas_id' => $kelasId]
        );
    }
    
    public function isNipExists($nip, $excludeId = null)
    {
        return $this->exists('nip', $nip, $excludeId);
    }
    
    public function createGuruWithUser($guruData, $userData)
    {
        $this->db->beginTransaction();
        
        try {
            // Create user first
            $userModel = new User();
            $userId = $userModel->createUser($userData);
            
            // Create guru with user_id
            $guruData['user_id'] = $userId;
            $guruId = $this->create($guruData);
            
            $this->db->commit();
            return $guruId;
        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }
    
    public function updateGuruWithUser($id, $guruData, $userData = null)
    {
        $this->db->beginTransaction();
        
        try {
            // Update guru data
            $this->update($id, $guruData);
            
            // Update user data if provided
            if ($userData) {
                $guru = $this->find($id);
                if ($guru['user_id']) {
                    $userModel = new User();
                    $userModel->updateUser($guru['user_id'], $userData);
                }
            }
            
            $this->db->commit();
            return true;
        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }
}
