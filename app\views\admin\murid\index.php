<?php
$title = 'Data Murid - ' . APP_NAME;
ob_start();
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">Data Murid</h1>
                    <p class="text-muted">Kelola data murid sekolah</p>
                </div>
                <div>
                    <a href="<?= APP_URL ?>/admin/murid/add" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i> Tambah Murid
                    </a>
                    <a href="<?= APP_URL ?>/admin/murid/import" class="btn btn-success">
                        <i class="bi bi-file-earmark-excel"></i> Import Excel
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Search and Filter -->
    <div class="row mb-4">
        <div class="col-md-6">
            <form method="GET" action="<?= APP_URL ?>/admin/murid">
                <div class="input-group">
                    <input type="text" class="form-control" name="search" 
                           placeholder="Cari nama atau NIS..." 
                           value="<?= htmlspecialchars($search) ?>">
                    <select class="form-select" name="kelas" style="max-width: 200px;">
                        <option value="">Semua Kelas</option>
                        <?php foreach ($kelasList as $kelas): ?>
                            <option value="<?= $kelas['id'] ?>" <?= $kelasFilter == $kelas['id'] ? 'selected' : '' ?>>
                                <?= htmlspecialchars($kelas['nama_kelas']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <button class="btn btn-outline-secondary" type="submit">
                        <i class="bi bi-search"></i>
                    </button>
                    <?php if (!empty($search) || !empty($kelasFilter)): ?>
                        <a href="<?= APP_URL ?>/admin/murid" class="btn btn-outline-danger">
                            <i class="bi bi-x"></i>
                        </a>
                    <?php endif; ?>
                </div>
            </form>
        </div>
        <div class="col-md-6 text-end">
            <small class="text-muted">
                Total: <?= $pagination['total'] ?> murid
            </small>
        </div>
    </div>
    
    <!-- Data Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <?php if (!empty($murid)): ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>No</th>
                                        <th>NIS</th>
                                        <th>Nama</th>
                                        <th>Kelas</th>
                                        <th>Jenis Kelamin</th>
                                        <th>Tempat, Tgl Lahir</th>
                                        <th>Orang Tua</th>
                                        <th>Aksi</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php 
                                    $no = ($pagination['current_page'] - 1) * $pagination['per_page'] + 1;
                                    foreach ($murid as $m): 
                                    ?>
                                        <tr>
                                            <td><?= $no++ ?></td>
                                            <td>
                                                <strong><?= htmlspecialchars($m['nis']) ?></strong>
                                            </td>
                                            <td>
                                                <div>
                                                    <strong><?= htmlspecialchars($m['nama']) ?></strong>
                                                    <?php if (!empty($m['alamat'])): ?>
                                                        <br><small class="text-muted">
                                                            <?= htmlspecialchars(substr($m['alamat'], 0, 30)) ?>
                                                            <?= strlen($m['alamat']) > 30 ? '...' : '' ?>
                                                        </small>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-primary">
                                                    <?= htmlspecialchars($m['nama_kelas']) ?>
                                                </span>
                                                <br><small class="text-muted">
                                                    Tingkat <?= htmlspecialchars($m['tingkat']) ?> - 
                                                    <?= htmlspecialchars($m['jurusan']) ?>
                                                </small>
                                            </td>
                                            <td>
                                                <span class="badge <?= $m['jenis_kelamin'] === 'L' ? 'bg-info' : 'bg-pink' ?>">
                                                    <?= $m['jenis_kelamin'] === 'L' ? 'Laki-laki' : 'Perempuan' ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if (!empty($m['tempat_lahir']) && !empty($m['tanggal_lahir'])): ?>
                                                    <small>
                                                        <?= htmlspecialchars($m['tempat_lahir']) ?>,<br>
                                                        <?= date('d/m/Y', strtotime($m['tanggal_lahir'])) ?>
                                                    </small>
                                                <?php else: ?>
                                                    <small class="text-muted">-</small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if (!empty($m['nama_ortu'])): ?>
                                                    <small>
                                                        <?= htmlspecialchars($m['nama_ortu']) ?>
                                                        <?php if (!empty($m['no_hp_ortu'])): ?>
                                                            <br><?= htmlspecialchars($m['no_hp_ortu']) ?>
                                                        <?php endif; ?>
                                                    </small>
                                                <?php else: ?>
                                                    <small class="text-muted">-</small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="<?= APP_URL ?>/admin/murid/edit/<?= $m['id'] ?>" 
                                                       class="btn btn-outline-primary" 
                                                       data-bs-toggle="tooltip" title="Edit">
                                                        <i class="bi bi-pencil"></i>
                                                    </a>
                                                    <a href="<?= APP_URL ?>/admin/murid/delete/<?= $m['id'] ?>" 
                                                       class="btn btn-outline-danger btn-delete" 
                                                       data-bs-toggle="tooltip" title="Hapus"
                                                       data-message="Apakah Anda yakin ingin menghapus murid <?= htmlspecialchars($m['nama']) ?>?">
                                                        <i class="bi bi-trash"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- Pagination -->
                        <?php if ($pagination['last_page'] > 1): ?>
                            <nav aria-label="Page navigation" class="mt-4">
                                <ul class="pagination justify-content-center">
                                    <?php if ($pagination['current_page'] > 1): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="<?= APP_URL ?>/admin/murid?page=<?= $pagination['current_page'] - 1 ?><?= !empty($search) ? '&search=' . urlencode($search) : '' ?><?= !empty($kelasFilter) ? '&kelas=' . $kelasFilter : '' ?>">
                                                <i class="bi bi-chevron-left"></i>
                                            </a>
                                        </li>
                                    <?php endif; ?>
                                    
                                    <?php for ($i = max(1, $pagination['current_page'] - 2); $i <= min($pagination['last_page'], $pagination['current_page'] + 2); $i++): ?>
                                        <li class="page-item <?= $i == $pagination['current_page'] ? 'active' : '' ?>">
                                            <a class="page-link" href="<?= APP_URL ?>/admin/murid?page=<?= $i ?><?= !empty($search) ? '&search=' . urlencode($search) : '' ?><?= !empty($kelasFilter) ? '&kelas=' . $kelasFilter : '' ?>">
                                                <?= $i ?>
                                            </a>
                                        </li>
                                    <?php endfor; ?>
                                    
                                    <?php if ($pagination['current_page'] < $pagination['last_page']): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="<?= APP_URL ?>/admin/murid?page=<?= $pagination['current_page'] + 1 ?><?= !empty($search) ? '&search=' . urlencode($search) : '' ?><?= !empty($kelasFilter) ? '&kelas=' . $kelasFilter : '' ?>">
                                                <i class="bi bi-chevron-right"></i>
                                            </a>
                                        </li>
                                    <?php endif; ?>
                                </ul>
                            </nav>
                        <?php endif; ?>
                        
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="bi bi-people text-muted" style="font-size: 4rem;"></i>
                            <h5 class="mt-3 text-muted">Belum ada data murid</h5>
                            <p class="text-muted">Klik tombol "Tambah Murid" untuk menambahkan data murid baru atau gunakan "Import Excel" untuk import data dalam jumlah banyak</p>
                            <div class="d-flex justify-content-center gap-2">
                                <a href="<?= APP_URL ?>/admin/murid/add" class="btn btn-primary">
                                    <i class="bi bi-plus-circle"></i> Tambah Murid
                                </a>
                                <a href="<?= APP_URL ?>/admin/murid/import" class="btn btn-success">
                                    <i class="bi bi-file-earmark-excel"></i> Import Excel
                                </a>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.bg-pink {
    background-color: #e91e63 !important;
}
</style>

<?php
$content = ob_get_clean();
include VIEWS_PATH . '/layouts/main.php';
?>
