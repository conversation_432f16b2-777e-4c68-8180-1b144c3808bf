<?php
$title = '<PERSON> Kelas - ' . APP_NAME;
ob_start();
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">Data Kelas</h1>
                    <p class="text-muted">Kelola data kelas sekolah</p>
                </div>
                <a href="<?= APP_URL ?>/admin/kelas/add" class="btn btn-primary">
                    <i class="bi bi-plus-circle"></i> <PERSON><PERSON> Kelas
                </a>
            </div>
        </div>
    </div>
    
    <!-- Search and Filter -->
    <div class="row mb-4">
        <div class="col-md-6">
            <form method="GET" action="<?= APP_URL ?>/admin/kelas">
                <div class="input-group">
                    <input type="text" class="form-control" name="search" 
                           placeholder="Cari nama kelas, tingkat, atau jurusan..." 
                           value="<?= htmlspecialchars($search) ?>">
                    <button class="btn btn-outline-secondary" type="submit">
                        <i class="bi bi-search"></i>
                    </button>
                    <?php if (!empty($search)): ?>
                        <a href="<?= APP_URL ?>/admin/kelas" class="btn btn-outline-danger">
                            <i class="bi bi-x"></i>
                        </a>
                    <?php endif; ?>
                </div>
            </form>
        </div>
        <div class="col-md-6 text-end">
            <small class="text-muted">
                Total: <?= $pagination['total'] ?> kelas
            </small>
        </div>
    </div>
    
    <!-- Data Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <?php if (!empty($kelas)): ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>No</th>
                                        <th>Nama Kelas</th>
                                        <th>Tingkat</th>
                                        <th>Jurusan</th>
                                        <th>Tahun Ajaran</th>
                                        <th>Jumlah Murid</th>
                                        <th>Jumlah Guru</th>
                                        <th>Aksi</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php 
                                    $no = ($pagination['current_page'] - 1) * $pagination['per_page'] + 1;
                                    foreach ($kelas as $k): 
                                    ?>
                                        <tr>
                                            <td><?= $no++ ?></td>
                                            <td>
                                                <strong><?= htmlspecialchars($k['nama_kelas']) ?></strong>
                                            </td>
                                            <td>
                                                <span class="badge bg-primary"><?= htmlspecialchars($k['tingkat']) ?></span>
                                            </td>
                                            <td><?= htmlspecialchars($k['jurusan']) ?></td>
                                            <td><?= htmlspecialchars($k['tahun_ajaran']) ?></td>
                                            <td>
                                                <span class="badge bg-info">
                                                    <i class="bi bi-people"></i> <?= $k['total_murid'] ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-success">
                                                    <i class="bi bi-person-badge"></i> <?= $k['total_guru'] ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="<?= APP_URL ?>/admin/kelas/edit/<?= $k['id'] ?>" 
                                                       class="btn btn-outline-primary" 
                                                       data-bs-toggle="tooltip" title="Edit">
                                                        <i class="bi bi-pencil"></i>
                                                    </a>
                                                    <?php if ($k['total_murid'] == 0): ?>
                                                        <a href="<?= APP_URL ?>/admin/kelas/delete/<?= $k['id'] ?>" 
                                                           class="btn btn-outline-danger btn-delete" 
                                                           data-bs-toggle="tooltip" title="Hapus"
                                                           data-message="Apakah Anda yakin ingin menghapus kelas <?= htmlspecialchars($k['nama_kelas']) ?>?">
                                                            <i class="bi bi-trash"></i>
                                                        </a>
                                                    <?php else: ?>
                                                        <button class="btn btn-outline-secondary" disabled
                                                                data-bs-toggle="tooltip" title="Tidak dapat dihapus karena masih ada murid">
                                                            <i class="bi bi-trash"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- Pagination -->
                        <?php if ($pagination['last_page'] > 1): ?>
                            <nav aria-label="Page navigation" class="mt-4">
                                <ul class="pagination justify-content-center">
                                    <?php if ($pagination['current_page'] > 1): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="<?= APP_URL ?>/admin/kelas?page=<?= $pagination['current_page'] - 1 ?><?= !empty($search) ? '&search=' . urlencode($search) : '' ?>">
                                                <i class="bi bi-chevron-left"></i>
                                            </a>
                                        </li>
                                    <?php endif; ?>
                                    
                                    <?php for ($i = max(1, $pagination['current_page'] - 2); $i <= min($pagination['last_page'], $pagination['current_page'] + 2); $i++): ?>
                                        <li class="page-item <?= $i == $pagination['current_page'] ? 'active' : '' ?>">
                                            <a class="page-link" href="<?= APP_URL ?>/admin/kelas?page=<?= $i ?><?= !empty($search) ? '&search=' . urlencode($search) : '' ?>">
                                                <?= $i ?>
                                            </a>
                                        </li>
                                    <?php endfor; ?>
                                    
                                    <?php if ($pagination['current_page'] < $pagination['last_page']): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="<?= APP_URL ?>/admin/kelas?page=<?= $pagination['current_page'] + 1 ?><?= !empty($search) ? '&search=' . urlencode($search) : '' ?>">
                                                <i class="bi bi-chevron-right"></i>
                                            </a>
                                        </li>
                                    <?php endif; ?>
                                </ul>
                            </nav>
                        <?php endif; ?>
                        
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="bi bi-building text-muted" style="font-size: 4rem;"></i>
                            <h5 class="mt-3 text-muted">Belum ada data kelas</h5>
                            <p class="text-muted">Klik tombol "Tambah Kelas" untuk menambahkan data kelas baru</p>
                            <a href="<?= APP_URL ?>/admin/kelas/add" class="btn btn-primary">
                                <i class="bi bi-plus-circle"></i> Tambah Kelas
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$content = ob_get_clean();
include VIEWS_PATH . '/layouts/main.php';
?>
