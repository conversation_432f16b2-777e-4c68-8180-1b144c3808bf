<?php

class User extends Model
{
    protected $table = 'users';
    
    public function authenticate($username, $password)
    {
        $user = $this->findBy('username', $username);
        
        if ($user && password_verify($password, $user['password']) && $user['is_active']) {
            return $user;
        }
        
        return false;
    }
    
    public function createUser($data)
    {
        // Hash password
        if (isset($data['password'])) {
            $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
        }
        
        return $this->create($data);
    }
    
    public function updateUser($id, $data)
    {
        // Hash password if provided
        if (isset($data['password']) && !empty($data['password'])) {
            $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
        } else {
            // Remove password from update if empty
            unset($data['password']);
        }
        
        return $this->update($id, $data);
    }
    
    public function getUserWithRole($userId)
    {
        $sql = "SELECT u.*, 
                CASE 
                    WHEN u.role = 'guru' THEN g.nama
                    ELSE u.nama 
                END as display_name,
                CASE 
                    WHEN u.role = 'guru' THEN g.id
                    ELSE NULL 
                END as guru_id
                FROM users u 
                LEFT JOIN guru g ON u.id = g.user_id 
                WHERE u.id = :id AND u.is_active = 1";
        
        return $this->db->fetch($sql, ['id' => $userId]);
    }
    
    public function isUsernameExists($username, $excludeId = null)
    {
        return $this->exists('username', $username, $excludeId);
    }
    
    public function getActiveUsers($role = null)
    {
        $conditions = 'is_active = 1';
        $params = [];
        
        if ($role) {
            $conditions .= ' AND role = :role';
            $params['role'] = $role;
        }
        
        return $this->all($conditions, $params);
    }
}
