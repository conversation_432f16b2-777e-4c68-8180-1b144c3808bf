<?php

class Kelas extends Model
{
    protected $table = 'kelas';
    
    public function getKelasWithStats()
    {
        $sql = "SELECT k.*, 
                COUNT(m.id) as total_murid,
                COUNT(DISTINCT gk.guru_id) as total_guru
                FROM kelas k
                LEFT JOIN murid m ON k.id = m.kelas_id AND m.is_active = 1
                LEFT JOIN guru_kelas gk ON k.id = gk.kelas_id
                WHERE k.is_active = 1
                GROUP BY k.id
                ORDER BY k.tingkat, k.nama_kelas";
        
        return $this->db->fetchAll($sql);
    }
    
    public function getKelasWithMurid($id)
    {
        $kelas = $this->find($id);
        if (!$kelas) {
            return null;
        }
        
        $murid = $this->db->fetchAll(
            "SELECT * FROM murid WHERE kelas_id = :kelas_id AND is_active = 1 ORDER BY nama",
            ['kelas_id' => $id]
        );
        
        $guru = $this->db->fetchAll(
            "SELECT g.nama, g.mata_pelajaran, gk.mata_pelajaran as mengajar_mapel
             FROM guru_kelas gk
             JOIN guru g ON gk.guru_id = g.id
             WHERE gk.kelas_id = :kelas_id AND g.is_active = 1",
            ['kelas_id' => $id]
        );
        
        return [
            'kelas' => $kelas,
            'murid' => $murid,
            'guru' => $guru
        ];
    }
    
    public function isNamaKelasExists($namaKelas, $tingkat, $tahunAjaran, $excludeId = null)
    {
        $sql = "SELECT COUNT(*) as count FROM kelas 
                WHERE nama_kelas = :nama_kelas 
                AND tingkat = :tingkat 
                AND tahun_ajaran = :tahun_ajaran";
        
        $params = [
            'nama_kelas' => $namaKelas,
            'tingkat' => $tingkat,
            'tahun_ajaran' => $tahunAjaran
        ];
        
        if ($excludeId) {
            $sql .= " AND id != :exclude_id";
            $params['exclude_id'] = $excludeId;
        }
        
        $result = $this->db->fetch($sql, $params);
        return $result['count'] > 0;
    }
    
    public function getAvailableKelas($guruId = null)
    {
        $sql = "SELECT k.* FROM kelas k WHERE k.is_active = 1";
        $params = [];
        
        if ($guruId) {
            $sql .= " AND k.id NOT IN (
                SELECT kelas_id FROM guru_kelas WHERE guru_id = :guru_id
            )";
            $params['guru_id'] = $guruId;
        }
        
        $sql .= " ORDER BY k.tingkat, k.nama_kelas";
        
        return $this->db->fetchAll($sql, $params);
    }
    
    public function getTingkatList()
    {
        $sql = "SELECT DISTINCT tingkat FROM kelas WHERE is_active = 1 ORDER BY tingkat";
        return $this->db->fetchAll($sql);
    }
    
    public function getJurusanList()
    {
        $sql = "SELECT DISTINCT jurusan FROM kelas WHERE is_active = 1 AND jurusan IS NOT NULL ORDER BY jurusan";
        return $this->db->fetchAll($sql);
    }
    
    public function getTahunAjaranList()
    {
        $sql = "SELECT DISTINCT tahun_ajaran FROM kelas WHERE is_active = 1 ORDER BY tahun_ajaran DESC";
        return $this->db->fetchAll($sql);
    }
}
