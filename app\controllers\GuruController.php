<?php

class GuruController extends Controller
{
    private $guruModel;
    private $kelasModel;
    private $muridModel;
    private $nilaiCatatanModel;
    
    public function __construct()
    {
        parent::__construct();
        $this->requireRole('guru');
        
        $this->guruModel = new Guru();
        $this->kelasModel = new Kelas();
        $this->muridModel = new Murid();
        $this->nilaiCatatanModel = new NilaiCatatan();
    }
    
    public function kelas()
    {
        $guruId = $_SESSION['guru_id'];
        
        // Get kelas yang diampu guru
        $kelasList = $this->guruModel->fetchAll(
            "SELECT k.id, k.nama_kelas, k.tingkat, k.jurusan, k.tahun_ajaran,
                    gk.mata_pelajaran,
                    COUNT(m.id) as total_murid
             FROM guru_kelas gk
             JOIN kelas k ON gk.kelas_id = k.id
             LEFT JOIN murid m ON k.id = m.kelas_id AND m.is_active = 1
             WHERE gk.guru_id = :guru_id AND k.is_active = 1
             GROUP BY k.id, gk.mata_pelajaran
             ORDER BY k.nama_kelas",
            ['guru_id' => $guruId]
        );
        
        $this->view('guru/kelas/index', [
            'kelasList' => $kelasList
        ]);
    }
    
    public function murid($kelasId)
    {
        $guruId = $_SESSION['guru_id'];
        
        // Verify guru mengajar kelas ini
        $kelasAccess = $this->guruModel->fetch(
            "SELECT k.*, gk.mata_pelajaran
             FROM guru_kelas gk
             JOIN kelas k ON gk.kelas_id = k.id
             WHERE gk.guru_id = :guru_id AND k.id = :kelas_id AND k.is_active = 1",
            ['guru_id' => $guruId, 'kelas_id' => $kelasId]
        );
        
        if (!$kelasAccess) {
            $this->redirect('guru/kelas');
        }
        
        // Get murid dalam kelas
        $muridList = $this->muridModel->getMuridForGuru($guruId, $kelasId);
        
        // Get statistik nilai untuk setiap murid
        foreach ($muridList as &$murid) {
            $stats = $this->nilaiCatatanModel->fetch(
                "SELECT 
                    COUNT(CASE WHEN jenis = 'nilai' THEN 1 END) as total_nilai,
                    AVG(CASE WHEN jenis = 'nilai' THEN nilai END) as rata_nilai,
                    COUNT(CASE WHEN jenis = 'catatan' THEN 1 END) as total_catatan,
                    COUNT(CASE WHEN jenis = 'absensi' THEN 1 END) as total_absensi,
                    MAX(tanggal) as last_update
                 FROM nilai_catatan 
                 WHERE murid_id = :murid_id AND guru_id = :guru_id",
                ['murid_id' => $murid['id'], 'guru_id' => $guruId]
            );
            $murid['stats'] = $stats;
        }
        
        $this->view('guru/murid/index', [
            'kelas' => $kelasAccess,
            'muridList' => $muridList
        ]);
    }
    
    public function nilai($muridId)
    {
        $guruId = $_SESSION['guru_id'];
        
        // Get murid data dan verify akses
        $murid = $this->muridModel->fetch(
            "SELECT m.*, k.nama_kelas, k.tingkat, k.jurusan
             FROM murid m
             JOIN kelas k ON m.kelas_id = k.id
             JOIN guru_kelas gk ON k.id = gk.kelas_id
             WHERE m.id = :murid_id AND gk.guru_id = :guru_id AND m.is_active = 1",
            ['murid_id' => $muridId, 'guru_id' => $guruId]
        );
        
        if (!$murid) {
            $this->redirect('guru/kelas');
        }
        
        // Get mata pelajaran yang diajar guru di kelas ini
        $mataPelajaran = $this->guruModel->fetch(
            "SELECT mata_pelajaran FROM guru_kelas 
             WHERE guru_id = :guru_id AND kelas_id = :kelas_id",
            ['guru_id' => $guruId, 'kelas_id' => $murid['kelas_id']]
        )['mata_pelajaran'];
        
        $error = '';
        $success = '';
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            try {
                $this->validateCSRF();
                
                $jenis = $_POST['jenis'];
                $tanggal = $_POST['tanggal'];
                $nilai = $_POST['nilai'] ?? null;
                $catatan = $_POST['catatan'] ?? '';
                
                // Validation
                $errors = $this->validate([
                    'jenis' => $jenis,
                    'tanggal' => $tanggal
                ], [
                    'jenis' => 'required',
                    'tanggal' => 'required'
                ]);
                
                if ($jenis === 'nilai' && (empty($nilai) || !is_numeric($nilai))) {
                    $errors['nilai'] = 'Nilai wajib diisi dan harus berupa angka';
                }
                
                if (($jenis === 'catatan' || $jenis === 'absensi') && empty($catatan)) {
                    $errors['catatan'] = 'Catatan wajib diisi';
                }
                
                if (empty($errors)) {
                    $data = [
                        'murid_id' => $muridId,
                        'guru_id' => $guruId,
                        'mata_pelajaran' => $mataPelajaran,
                        'jenis' => $jenis,
                        'tanggal' => $tanggal
                    ];
                    
                    if ($jenis === 'nilai') {
                        $data['nilai'] = $nilai;
                    } else {
                        $data['catatan'] = $catatan;
                    }
                    
                    $this->nilaiCatatanModel->create($data);
                    $success = ucfirst($jenis) . ' berhasil ditambahkan';
                    
                    // Reset form
                    $_POST = [];
                }
                
                if (!empty($errors)) {
                    $error = implode(', ', $errors);
                }
                
            } catch (Exception $e) {
                $error = 'Terjadi kesalahan: ' . $e->getMessage();
            }
        }
        
        // Get riwayat nilai dan catatan
        $riwayat = $this->nilaiCatatanModel->getNilaiCatatanWithDetails($muridId, $guruId);
        
        $this->view('guru/nilai/form', [
            'murid' => $murid,
            'mataPelajaran' => $mataPelajaran,
            'riwayat' => $riwayat,
            'error' => $error,
            'success' => $success,
            'csrf_token' => $this->generateCSRF()
        ]);
    }
    
    public function rekap($kelasId)
    {
        $guruId = $_SESSION['guru_id'];
        
        // Verify guru mengajar kelas ini
        $kelas = $this->guruModel->fetch(
            "SELECT k.*, gk.mata_pelajaran
             FROM guru_kelas gk
             JOIN kelas k ON gk.kelas_id = k.id
             WHERE gk.guru_id = :guru_id AND k.id = :kelas_id AND k.is_active = 1",
            ['guru_id' => $guruId, 'kelas_id' => $kelasId]
        );
        
        if (!$kelas) {
            $this->redirect('guru/kelas');
        }
        
        // Get rekap data
        $rekap = $this->nilaiCatatanModel->getRekapByKelas($kelasId, $guruId);
        
        // Get detail nilai per murid
        foreach ($rekap as &$murid) {
            $nilaiDetail = $this->nilaiCatatanModel->fetchAll(
                "SELECT nilai, tanggal FROM nilai_catatan 
                 WHERE murid_id = :murid_id AND guru_id = :guru_id AND jenis = 'nilai'
                 ORDER BY tanggal DESC LIMIT 5",
                ['murid_id' => $murid['id'], 'guru_id' => $guruId]
            );
            $murid['nilai_detail'] = $nilaiDetail;
        }
        
        $this->view('guru/rekap/index', [
            'kelas' => $kelas,
            'rekap' => $rekap
        ]);
    }
    
    public function exportPdf($kelasId)
    {
        $guruId = $_SESSION['guru_id'];
        
        // Verify access dan get data
        $kelas = $this->guruModel->fetch(
            "SELECT k.*, gk.mata_pelajaran
             FROM guru_kelas gk
             JOIN kelas k ON gk.kelas_id = k.id
             WHERE gk.guru_id = :guru_id AND k.id = :kelas_id AND k.is_active = 1",
            ['guru_id' => $guruId, 'kelas_id' => $kelasId]
        );
        
        if (!$kelas) {
            $this->redirect('guru/kelas');
        }
        
        $rekap = $this->nilaiCatatanModel->getRekapByKelas($kelasId, $guruId);
        
        // Generate PDF
        require_once 'vendor/autoload.php';
        
        $dompdf = new \Dompdf\Dompdf();
        $dompdf->loadHtml($this->generatePdfContent($kelas, $rekap));
        $dompdf->setPaper('A4', 'portrait');
        $dompdf->render();
        
        $filename = 'Rekap_' . $kelas['nama_kelas'] . '_' . date('Y-m-d') . '.pdf';
        $dompdf->stream($filename, ['Attachment' => true]);
    }
    
    public function exportExcel($kelasId)
    {
        $guruId = $_SESSION['guru_id'];
        
        // Verify access dan get data
        $kelas = $this->guruModel->fetch(
            "SELECT k.*, gk.mata_pelajaran
             FROM guru_kelas gk
             JOIN kelas k ON gk.kelas_id = k.id
             WHERE gk.guru_id = :guru_id AND k.id = :kelas_id AND k.is_active = 1",
            ['guru_id' => $guruId, 'kelas_id' => $kelasId]
        );
        
        if (!$kelas) {
            $this->redirect('guru/kelas');
        }
        
        $rekap = $this->nilaiCatatanModel->getRekapByKelas($kelasId, $guruId);
        
        // Generate Excel
        require_once 'vendor/autoload.php';
        
        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        
        // Header
        $sheet->setCellValue('A1', 'REKAP NILAI KELAS ' . $kelas['nama_kelas']);
        $sheet->mergeCells('A1:G1');
        $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(16);
        $sheet->getStyle('A1')->getAlignment()->setHorizontal('center');
        
        $sheet->setCellValue('A2', 'Mata Pelajaran: ' . $kelas['mata_pelajaran']);
        $sheet->setCellValue('A3', 'Tanggal: ' . date('d F Y'));
        
        // Table header
        $headers = ['No', 'NIS', 'Nama', 'Jenis Kelamin', 'Total Nilai', 'Rata-rata', 'Total Catatan'];
        $col = 'A';
        foreach ($headers as $header) {
            $sheet->setCellValue($col . '5', $header);
            $sheet->getStyle($col . '5')->getFont()->setBold(true);
            $col++;
        }
        
        // Data
        $row = 6;
        $no = 1;
        foreach ($rekap as $murid) {
            $sheet->setCellValue('A' . $row, $no++);
            $sheet->setCellValue('B' . $row, $murid['nis']);
            $sheet->setCellValue('C' . $row, $murid['nama']);
            $sheet->setCellValue('D' . $row, $murid['jenis_kelamin'] === 'L' ? 'Laki-laki' : 'Perempuan');
            $sheet->setCellValue('E' . $row, $murid['total_nilai']);
            $sheet->setCellValue('F' . $row, $murid['rata_nilai'] ? round($murid['rata_nilai'], 2) : '-');
            $sheet->setCellValue('G' . $row, $murid['total_catatan']);
            $row++;
        }
        
        // Auto width
        foreach (range('A', 'G') as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }
        
        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
        
        $filename = 'Rekap_' . $kelas['nama_kelas'] . '_' . date('Y-m-d') . '.xlsx';
        
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');
        
        $writer->save('php://output');
        exit;
    }
    
    private function generatePdfContent($kelas, $rekap)
    {
        $html = '
        <html>
        <head>
            <style>
                body { font-family: Arial, sans-serif; font-size: 12px; }
                .header { text-align: center; margin-bottom: 20px; }
                .info { margin-bottom: 15px; }
                table { width: 100%; border-collapse: collapse; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; font-weight: bold; }
                .text-center { text-align: center; }
                .text-right { text-align: right; }
            </style>
        </head>
        <body>
            <div class="header">
                <h2>REKAP NILAI KELAS ' . htmlspecialchars($kelas['nama_kelas']) . '</h2>
                <p>Mata Pelajaran: ' . htmlspecialchars($kelas['mata_pelajaran']) . '</p>
            </div>
            
            <div class="info">
                <p><strong>Tingkat:</strong> ' . htmlspecialchars($kelas['tingkat']) . '</p>
                <p><strong>Jurusan:</strong> ' . htmlspecialchars($kelas['jurusan']) . '</p>
                <p><strong>Tahun Ajaran:</strong> ' . htmlspecialchars($kelas['tahun_ajaran']) . '</p>
                <p><strong>Tanggal Cetak:</strong> ' . date('d F Y') . '</p>
            </div>
            
            <table>
                <thead>
                    <tr>
                        <th class="text-center">No</th>
                        <th>NIS</th>
                        <th>Nama</th>
                        <th class="text-center">JK</th>
                        <th class="text-center">Total Nilai</th>
                        <th class="text-center">Rata-rata</th>
                        <th class="text-center">Total Catatan</th>
                    </tr>
                </thead>
                <tbody>';
        
        $no = 1;
        foreach ($rekap as $murid) {
            $html .= '
                    <tr>
                        <td class="text-center">' . $no++ . '</td>
                        <td>' . htmlspecialchars($murid['nis']) . '</td>
                        <td>' . htmlspecialchars($murid['nama']) . '</td>
                        <td class="text-center">' . htmlspecialchars($murid['jenis_kelamin']) . '</td>
                        <td class="text-center">' . $murid['total_nilai'] . '</td>
                        <td class="text-center">' . ($murid['rata_nilai'] ? round($murid['rata_nilai'], 2) : '-') . '</td>
                        <td class="text-center">' . $murid['total_catatan'] . '</td>
                    </tr>';
        }
        
        $html .= '
                </tbody>
            </table>
            
            <div style="margin-top: 30px;">
                <p><strong>Total Murid:</strong> ' . count($rekap) . '</p>
                <p><strong>Dicetak oleh:</strong> ' . htmlspecialchars($_SESSION['nama']) . '</p>
            </div>
        </body>
        </html>';
        
        return $html;
    }
}
