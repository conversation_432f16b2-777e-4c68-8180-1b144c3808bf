<?php
$title = 'Tambah Murid - ' . APP_NAME;
ob_start();
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">Tambah Murid</h1>
                    <p class="text-muted">Tambahkan data murid baru ke sistem</p>
                </div>
                <div>
                    <a href="<?= APP_URL ?>/admin/murid" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i> Kembali
                    </a>
                    <a href="<?= APP_URL ?>/admin/murid/import" class="btn btn-success">
                        <i class="bi bi-file-earmark-excel"></i> Import Excel
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-body">
                    <?php if (!empty($error)): ?>
                        <div class="alert alert-danger alert-dismissible fade show">
                            <i class="bi bi-exclamation-circle"></i>
                            <?= htmlspecialchars($error) ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($success)): ?>
                        <div class="alert alert-success alert-dismissible fade show">
                            <i class="bi bi-check-circle"></i>
                            <?= htmlspecialchars($success) ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <form method="POST" action="<?= APP_URL ?>/admin/murid/add" class="needs-validation" novalidate>
                        <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">
                        
                        <div class="row">
                            <div class="col-md-6">
                                <h5 class="mb-3">Data Utama</h5>
                                
                                <div class="mb-3">
                                    <label for="nis" class="form-label">NIS <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="nis" name="nis" 
                                           value="<?= htmlspecialchars($_POST['nis'] ?? '') ?>" required
                                           placeholder="Nomor Induk Siswa">
                                    <div class="form-text">NIS harus unik untuk setiap murid</div>
                                    <div class="invalid-feedback">NIS wajib diisi</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="nama" class="form-label">Nama Lengkap <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="nama" name="nama" 
                                           value="<?= htmlspecialchars($_POST['nama'] ?? '') ?>" required
                                           placeholder="Nama lengkap murid">
                                    <div class="invalid-feedback">Nama lengkap wajib diisi</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="kelas_id" class="form-label">Kelas <span class="text-danger">*</span></label>
                                    <select class="form-select" id="kelas_id" name="kelas_id" required>
                                        <option value="">Pilih kelas</option>
                                        <?php foreach ($kelasList as $kelas): ?>
                                            <option value="<?= $kelas['id'] ?>" <?= ($_POST['kelas_id'] ?? '') == $kelas['id'] ? 'selected' : '' ?>>
                                                <?= htmlspecialchars($kelas['nama_kelas']) ?> - 
                                                Tingkat <?= htmlspecialchars($kelas['tingkat']) ?> 
                                                <?= $kelas['jurusan'] ? htmlspecialchars($kelas['jurusan']) : '' ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="invalid-feedback">Kelas wajib dipilih</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="jenis_kelamin" class="form-label">Jenis Kelamin <span class="text-danger">*</span></label>
                                    <select class="form-select" id="jenis_kelamin" name="jenis_kelamin" required>
                                        <option value="">Pilih jenis kelamin</option>
                                        <option value="L" <?= ($_POST['jenis_kelamin'] ?? '') === 'L' ? 'selected' : '' ?>>Laki-laki</option>
                                        <option value="P" <?= ($_POST['jenis_kelamin'] ?? '') === 'P' ? 'selected' : '' ?>>Perempuan</option>
                                    </select>
                                    <div class="invalid-feedback">Jenis kelamin wajib dipilih</div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <h5 class="mb-3">Data Tambahan</h5>
                                
                                <div class="mb-3">
                                    <label for="tempat_lahir" class="form-label">Tempat Lahir</label>
                                    <input type="text" class="form-control" id="tempat_lahir" name="tempat_lahir" 
                                           value="<?= htmlspecialchars($_POST['tempat_lahir'] ?? '') ?>"
                                           placeholder="Kota tempat lahir">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="tanggal_lahir" class="form-label">Tanggal Lahir</label>
                                    <input type="date" class="form-control" id="tanggal_lahir" name="tanggal_lahir" 
                                           value="<?= $_POST['tanggal_lahir'] ?? '' ?>">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="alamat" class="form-label">Alamat</label>
                                    <textarea class="form-control" id="alamat" name="alamat" rows="3"
                                              placeholder="Alamat lengkap murid"><?= htmlspecialchars($_POST['alamat'] ?? '') ?></textarea>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="no_hp_ortu" class="form-label">No. HP Orang Tua</label>
                                    <input type="text" class="form-control" id="no_hp_ortu" name="no_hp_ortu" 
                                           value="<?= htmlspecialchars($_POST['no_hp_ortu'] ?? '') ?>"
                                           placeholder="Nomor HP yang bisa dihubungi">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="nama_ortu" class="form-label">Nama Orang Tua/Wali</label>
                                    <input type="text" class="form-control" id="nama_ortu" name="nama_ortu" 
                                           value="<?= htmlspecialchars($_POST['nama_ortu'] ?? '') ?>"
                                           placeholder="Nama orang tua atau wali">
                                </div>
                            </div>
                        </div>
                        
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle"></i>
                            <strong>Informasi:</strong><br>
                            • Field yang bertanda <span class="text-danger">*</span> wajib diisi<br>
                            • NIS harus unik dan tidak boleh sama dengan murid lain<br>
                            • Data tambahan dapat diisi kemudian melalui menu edit<br>
                            • Untuk menambah banyak murid sekaligus, gunakan fitur Import Excel
                        </div>
                        
                        <hr>
                        
                        <div class="d-flex justify-content-between">
                            <a href="<?= APP_URL ?>/admin/murid" class="btn btn-secondary">
                                <i class="bi bi-arrow-left"></i> Batal
                            </a>
                            <button type="submit" class="btn btn-primary" data-original-text="Simpan Data">
                                <i class="bi bi-save"></i> Simpan Data
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-generate NIS based on current year
document.addEventListener('DOMContentLoaded', function() {
    const nisInput = document.getElementById('nis');
    if (nisInput.value === '') {
        const currentYear = new Date().getFullYear();
        const randomNum = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        nisInput.placeholder = `Contoh: ${currentYear}${randomNum}`;
    }
});

// Validate NIS format (numbers only)
document.getElementById('nis').addEventListener('input', function() {
    const value = this.value;
    if (value && !/^\d+$/.test(value)) {
        this.setCustomValidity('NIS hanya boleh berisi angka');
    } else {
        this.setCustomValidity('');
    }
});
</script>

<?php
$content = ob_get_clean();
include VIEWS_PATH . '/layouts/main.php';
?>
