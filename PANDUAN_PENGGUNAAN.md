# Panduan Penggunaan Sistem Absensi Siswa

## 📋 Daftar Isi
1. [Instalasi](#instalasi)
2. [<PERSON><PERSON> Pertama Kali](#login-pertama-kali)
3. [Panduan Admin](#panduan-admin)
4. [Panduan Guru](#panduan-guru)
5. [Import Data Excel](#import-data-excel)
6. [Export Data](#export-data)
7. [Troubleshooting](#troubleshooting)

## 🔧 Instalasi

### Persyaratan Sistem
- PHP 7.4 atau lebih tinggi
- MySQL 5.7 atau lebih tinggi
- Web server (Apache/Nginx)
- Composer

### Langkah Instalasi
1. **Download dan Extract**
   ```bash
   # Extract file ke folder web server
   # Contoh: C:\laragon\www\Absensi siswa berbasis web
   ```

2. **Install Dependencies**
   ```bash
   composer install
   ```

3. **Konfigurasi Database**
   - Edit file `app/config/config.php`
   - Sesuaikan pengaturan database:
   ```php
   define('DB_HOST', 'localhost');
   define('DB_NAME', 'absensi_siswa');
   define('DB_USER', 'root');
   define('DB_PASS', '');
   ```

4. **Setup Database**
   - Buka browser dan akses: `http://localhost/Absensi%20siswa%20berbasis%20web/install.php`
   - Ikuti proses instalasi otomatis
   - Atau import manual file `database/schema.sql`

5. **Akses Aplikasi**
   - URL: `http://localhost/Absensi%20siswa%20berbasis%20web/`

## 🔐 Login Pertama Kali

### Akun Default
- **Admin**
  - Username: `admin`
  - Password: `password`
  
- **Guru Demo**
  - Username: `guru1`
  - Password: `password`

### Mengubah Password Default
1. Login sebagai admin
2. Masuk ke menu Data Guru
3. Edit data guru yang ingin diubah passwordnya
4. Isi password baru (minimal 6 karakter)
5. Simpan perubahan

## 👨‍💼 Panduan Admin

### Dashboard Admin
- Melihat statistik total guru, kelas, murid, dan users
- Melihat daftar murid terbaru yang ditambahkan
- Akses cepat ke menu utama

### Kelola Data Guru
1. **Menambah Guru Baru**
   - Klik menu "Kelola Data" → "Data Guru"
   - Klik tombol "Tambah Guru"
   - Isi form data guru dan akun login
   - Klik "Simpan Data"

2. **Edit Data Guru**
   - Di halaman Data Guru, klik tombol edit (ikon pensil)
   - Ubah data yang diperlukan
   - Password bisa dikosongkan jika tidak ingin diubah
   - Klik "Update Data"

3. **Hapus Data Guru**
   - Klik tombol hapus (ikon tempat sampah)
   - Konfirmasi penghapusan
   - Data guru akan dinonaktifkan (soft delete)

### Kelola Data Kelas
1. **Menambah Kelas Baru**
   - Klik menu "Kelola Data" → "Data Kelas"
   - Klik tombol "Tambah Kelas"
   - Isi nama kelas, tingkat, jurusan, dan tahun ajaran
   - Klik "Simpan Data"

2. **Edit Data Kelas**
   - Di halaman Data Kelas, klik tombol edit
   - Ubah data yang diperlukan
   - Klik "Update Data"

3. **Hapus Data Kelas**
   - Kelas hanya bisa dihapus jika tidak ada murid
   - Klik tombol hapus dan konfirmasi

### Kelola Data Murid
1. **Menambah Murid Manual**
   - Klik menu "Kelola Data" → "Data Murid"
   - Klik tombol "Tambah Murid"
   - Isi form data murid
   - Pilih kelas dari dropdown
   - Klik "Simpan Data"

2. **Import dari Excel**
   - Lihat bagian [Import Data Excel](#import-data-excel)

3. **Edit Data Murid**
   - Di halaman Data Murid, klik tombol edit
   - Ubah data yang diperlukan
   - Klik "Update Data"

4. **Filter dan Pencarian**
   - Gunakan kotak pencarian untuk mencari nama atau NIS
   - Filter berdasarkan kelas menggunakan dropdown

## 👨‍🏫 Panduan Guru

### Dashboard Guru
- Melihat jumlah kelas yang diampu
- Melihat total murid dari semua kelas
- Daftar kelas yang diampu dengan jumlah murid

### Melihat Data Murid
1. **Akses Kelas**
   - Dari dashboard, klik "Lihat Murid" pada kartu kelas
   - Atau melalui menu "Kelas Saya"

2. **Daftar Murid**
   - Melihat daftar murid dalam kelas
   - Informasi lengkap murid (NIS, nama, jenis kelamin, dll)

### Input Nilai dan Catatan
1. **Menambah Nilai**
   - Di halaman daftar murid, klik tombol "Input Nilai"
   - Pilih jenis input (Nilai/Catatan/Absensi)
   - Isi data yang diperlukan
   - Klik "Simpan"

2. **Melihat Riwayat**
   - Klik nama murid untuk melihat riwayat nilai dan catatan
   - Data ditampilkan berdasarkan tanggal terbaru

### Export Rekap Kelas
1. **Export PDF**
   - Di halaman kelas, klik "Export PDF"
   - File PDF akan didownload otomatis

2. **Export Excel**
   - Klik "Export Excel"
   - File Excel akan didownload otomatis

## 📊 Import Data Excel

### Format File Excel
File Excel harus memiliki kolom sesuai urutan berikut:

| Kolom | Nama Field | Wajib | Contoh |
|-------|------------|-------|---------|
| A | NIS | Ya | 2024001 |
| B | Nama | Ya | Ahmad Fauzi |
| C | ID Kelas | Ya | 1 |
| D | Jenis Kelamin | Ya | L atau P |
| E | Tempat Lahir | Tidak | Jakarta |
| F | Tanggal Lahir | Tidak | 2008-01-15 |
| G | Alamat | Tidak | Jl. Merdeka No. 1 |
| H | No HP Ortu | Tidak | 081234567890 |
| I | Nama Ortu | Tidak | Bapak Ahmad |

### Langkah Import
1. **Persiapan File**
   - Buat file Excel dengan format di atas
   - Baris pertama akan diabaikan (header)
   - Pastikan ID Kelas sesuai dengan kelas yang ada

2. **Upload File**
   - Masuk ke menu "Data Murid" → "Import Excel"
   - Pilih file Excel (.xls atau .xlsx)
   - Maksimal ukuran file 5MB
   - Klik "Upload & Preview"

3. **Preview Data**
   - Sistem akan menampilkan preview data
   - Data valid ditandai hijau, invalid ditandai merah
   - Periksa data sebelum melanjutkan

4. **Proses Import**
   - Klik "Import X Data" (X = jumlah data valid)
   - Sistem akan memproses dan menyimpan data
   - Data invalid akan diabaikan

### Tips Import
- Pastikan NIS unik (tidak duplikat)
- ID Kelas harus sesuai dengan kelas yang sudah ada
- Jenis kelamin hanya boleh "L" atau "P"
- Format tanggal: YYYY-MM-DD (contoh: 2008-01-15)

## 📤 Export Data

### Export PDF
- Berisi rekap data murid per kelas
- Format laporan yang rapi untuk dicetak
- Termasuk informasi kelas dan statistik

### Export Excel
- Data dalam format spreadsheet
- Bisa diedit lebih lanjut
- Cocok untuk analisis data

### Cara Export
1. **Admin**: Dari menu "Rekap Data"
2. **Guru**: Dari halaman kelas yang diampu
3. Pilih format (PDF atau Excel)
4. File akan didownload otomatis

## 🔧 Troubleshooting

### Error Database Connection
**Gejala**: Pesan error koneksi database
**Solusi**:
1. Pastikan MySQL server berjalan
2. Periksa konfigurasi di `app/config/config.php`
3. Pastikan database sudah dibuat
4. Periksa username dan password database

### Error 404 Not Found
**Gejala**: Halaman tidak ditemukan
**Solusi**:
1. Pastikan mod_rewrite Apache aktif
2. Periksa file `.htaccess` di root directory
3. Pastikan URL sesuai dengan struktur folder

### Error Upload File
**Gejala**: Gagal upload file Excel
**Solusi**:
1. Periksa permission folder `uploads/`
2. Pastikan ukuran file tidak melebihi 5MB
3. Pastikan format file .xls atau .xlsx
4. Periksa setting PHP `upload_max_filesize`

### Error Import Excel
**Gejala**: Gagal import data dari Excel
**Solusi**:
1. Periksa format file Excel sesuai panduan
2. Pastikan ID Kelas valid
3. Periksa NIS tidak duplikat
4. Pastikan jenis kelamin hanya "L" atau "P"

### Session Timeout
**Gejala**: Otomatis logout
**Solusi**:
1. Login ulang
2. Timeout default 1 jam
3. Bisa diubah di `app/config/config.php`

### Lupa Password
**Solusi**:
1. **Admin**: Reset melalui database atau buat ulang user admin
2. **Guru**: Minta admin untuk reset password melalui menu Edit Guru

## 📞 Bantuan Lebih Lanjut

Jika mengalami masalah yang tidak tercantum di panduan ini:
1. Periksa log error di web server
2. Pastikan semua persyaratan sistem terpenuhi
3. Buat issue di repository GitHub
4. Hubungi administrator sistem

---

**Selamat menggunakan Sistem Absensi Siswa!** 🎓
