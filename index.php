<?php
/**
 * Entry point untuk aplikasi Absensi Siswa
 * Sistem MVC sederhana dengan routing
 */

// Start session
session_start();

// Error reporting untuk development
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define constants
define('ROOT_PATH', __DIR__);
define('APP_PATH', ROOT_PATH . '/app');
define('PUBLIC_PATH', ROOT_PATH . '/public');
define('VIEWS_PATH', APP_PATH . '/views');
define('CONTROLLERS_PATH', APP_PATH . '/controllers');
define('MODELS_PATH', APP_PATH . '/models');

// Autoloader sederhana
spl_autoload_register(function ($class) {
    $paths = [
        APP_PATH . '/controllers/',
        APP_PATH . '/models/',
        APP_PATH . '/core/',
    ];
    
    foreach ($paths as $path) {
        $file = $path . $class . '.php';
        if (file_exists($file)) {
            require_once $file;
            return;
        }
    }
});

// Include config
require_once APP_PATH . '/config/config.php';

// Include core files
require_once APP_PATH . '/core/Router.php';
require_once APP_PATH . '/core/Controller.php';
require_once APP_PATH . '/core/Model.php';
require_once APP_PATH . '/core/Database.php';

// Initialize router
$router = new Router();

// Define routes
$router->add('', 'AuthController@login');
$router->add('login', 'AuthController@login');
$router->add('logout', 'AuthController@logout');
$router->add('dashboard', 'DashboardController@index');

// Admin routes
$router->add('admin/guru', 'AdminController@guru');
$router->add('admin/guru/add', 'AdminController@addGuru');
$router->add('admin/guru/edit/{id}', 'AdminController@editGuru');
$router->add('admin/guru/delete/{id}', 'AdminController@deleteGuru');

$router->add('admin/kelas', 'AdminController@kelas');
$router->add('admin/kelas/add', 'AdminController@addKelas');
$router->add('admin/kelas/edit/{id}', 'AdminController@editKelas');
$router->add('admin/kelas/delete/{id}', 'AdminController@deleteKelas');

$router->add('admin/murid', 'AdminController@murid');
$router->add('admin/murid/add', 'AdminController@addMurid');
$router->add('admin/murid/edit/{id}', 'AdminController@editMurid');
$router->add('admin/murid/delete/{id}', 'AdminController@deleteMurid');
$router->add('admin/murid/import', 'AdminController@importMurid');

$router->add('admin/rekap', 'AdminController@rekap');
$router->add('admin/export/pdf', 'AdminController@exportPdf');
$router->add('admin/export/excel', 'AdminController@exportExcel');

// Guru routes
$router->add('guru/kelas', 'GuruController@kelas');
$router->add('guru/murid/{kelas_id}', 'GuruController@murid');
$router->add('guru/nilai/{murid_id}', 'GuruController@nilai');
$router->add('guru/rekap/{kelas_id}', 'GuruController@rekap');
$router->add('guru/export/pdf/{kelas_id}', 'GuruController@exportPdf');
$router->add('guru/export/excel/{kelas_id}', 'GuruController@exportExcel');

// Get current URL
$url = $_GET['url'] ?? '';

// Dispatch route
$router->dispatch($url);
