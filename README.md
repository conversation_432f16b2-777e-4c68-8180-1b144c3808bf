# Sistem Absensi Siswa Berbasis Web

Aplikasi web untuk mengelola data siswa, guru, dan kelas dengan fitur absensi dan penilaian. Dibangun menggunakan PHP dengan struktur MVC sederhana, MySQL, dan Bootstrap 5.

## 🚀 Fitur Utama

### 👨‍💼 Admin
- **Kelola Data Guru**: CRUD data guru dengan akun login
- **Kelola Data Kelas**: Manajemen kelas berdasarkan tingkat dan jurusan
- **Kelola Data Murid**: CRUD data murid dengan relasi ke kelas
- **Import Excel**: Import data murid dari file Excel (.xls/.xlsx)
- **Export Data**: Export rekap data dalam format PDF dan Excel
- **Dashboard**: Statistik dan overview sistem

### 👨‍🏫 Guru
- **Akses <PERSON>rb<PERSON>**: Hanya dapat melihat kelas yang diajarkan
- **Input Nilai**: Menambah nilai dan catatan per murid
- **Rekap <PERSON>**: Melihat rekap nilai per kelas yang diampu
- **Export**: Export rekap kelas dalam format PDF dan Excel

## 🛠️ Teknologi

- **Backend**: PHP 7.4+
- **Database**: MySQL 5.7+
- **Frontend**: HTML5, CSS3, Bootstrap 5, JavaScript
- **Libraries**:
  - PhpSpreadsheet (Excel import/export)
  - Dompdf (PDF export)
  - Bootstrap Icons

## 📋 Persyaratan Sistem

- PHP 7.4 atau lebih tinggi
- MySQL 5.7 atau lebih tinggi
- Web server (Apache/Nginx)
- Composer
- Extension PHP: PDO, PDO_MySQL, mbstring, zip

## 🔧 Instalasi

### 1. Clone Repository
```bash
git clone <repository-url>
cd "Absensi siswa berbasis web"
```

### 2. Install Dependencies
```bash
composer install
```

### 3. Konfigurasi Database
Edit file `app/config/config.php` sesuai dengan konfigurasi database Anda:

```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'absensi_siswa');
define('DB_USER', 'root');
define('DB_PASS', '');
```

### 4. Setup Database
Jalankan script instalasi melalui browser:
```
http://localhost/Absensi%20siswa%20berbasis%20web/install.php
```

Atau import manual file `database/schema.sql` ke MySQL.

### 5. Akses Aplikasi
```
http://localhost/Absensi%20siswa%20berbasis%20web/
```

## 🔐 Login Default

### Admin
- **Username**: `admin`
- **Password**: `password`

### Guru Demo
- **Username**: `guru1`
- **Password**: `password`

## 📁 Struktur Project

```
Absensi siswa berbasis web/
├── app/
│   ├── config/
│   │   └── config.php          # Konfigurasi aplikasi
│   ├── controllers/
│   │   ├── AuthController.php  # Controller autentikasi
│   │   ├── DashboardController.php
│   │   ├── AdminController.php # Controller admin
│   │   └── GuruController.php  # Controller guru
│   ├── core/
│   │   ├── Controller.php      # Base controller
│   │   ├── Model.php          # Base model
│   │   ├── Database.php       # Database connection
│   │   └── Router.php         # URL routing
│   ├── models/
│   │   ├── User.php           # Model user
│   │   ├── Guru.php           # Model guru
│   │   ├── Kelas.php          # Model kelas
│   │   ├── Murid.php          # Model murid
│   │   └── NilaiCatatan.php   # Model nilai & catatan
│   └── views/
│       ├── layouts/
│       │   └── main.php       # Layout utama
│       ├── auth/
│       │   └── login.php      # Halaman login
│       ├── dashboard/
│       │   └── index.php      # Dashboard
│       ├── admin/             # Views admin
│       └── guru/              # Views guru
├── public/
│   ├── css/
│   │   └── style.css          # Custom CSS
│   ├── js/
│   │   └── app.js             # Custom JavaScript
│   └── images/                # Gambar
├── database/
│   └── schema.sql             # Database schema
├── uploads/                   # Folder upload file
├── vendor/                    # Composer dependencies
├── index.php                  # Entry point
├── install.php                # Script instalasi
├── composer.json              # Composer config
└── README.md                  # Dokumentasi
```

## 🎯 Cara Penggunaan

### Admin
1. Login sebagai admin
2. Kelola data guru melalui menu "Kelola Data > Data Guru"
3. Buat kelas melalui menu "Kelola Data > Data Kelas"
4. Tambah murid manual atau import dari Excel
5. Lihat rekap data melalui menu "Rekap Data"

### Guru
1. Login dengan akun yang dibuat admin
2. Lihat kelas yang diampu di dashboard
3. Akses daftar murid per kelas
4. Input nilai dan catatan murid
5. Export rekap kelas

### Import Excel
Format file Excel untuk import murid:
- Kolom A: NIS
- Kolom B: Nama
- Kolom C: ID Kelas
- Kolom D: Jenis Kelamin (L/P)
- Kolom E: Tempat Lahir
- Kolom F: Tanggal Lahir (YYYY-MM-DD)
- Kolom G: Alamat
- Kolom H: No HP Ortu
- Kolom I: Nama Ortu

## 🔒 Keamanan

- CSRF Protection pada semua form
- Password hashing menggunakan PHP password_hash()
- Session timeout otomatis
- Input sanitization dan validation
- SQL injection protection dengan prepared statements

## 🐛 Troubleshooting

### Error Database Connection
- Pastikan MySQL server berjalan
- Periksa konfigurasi database di `app/config/config.php`
- Pastikan database dan user memiliki permission yang tepat

### Error File Upload
- Periksa permission folder `uploads/`
- Pastikan ukuran file tidak melebihi batas PHP
- Periksa extension file yang diupload

### Error 404 Not Found
- Pastikan mod_rewrite Apache aktif
- Periksa file `.htaccess` di root directory

## 📝 Changelog

### Version 1.0.0
- Sistem login multi-user (Admin & Guru)
- CRUD data guru, kelas, dan murid
- Import data murid dari Excel
- Export data ke PDF dan Excel
- Dashboard dengan statistik
- Responsive design dengan Bootstrap 5

## 🤝 Kontribusi

1. Fork repository
2. Buat branch fitur (`git checkout -b feature/AmazingFeature`)
3. Commit perubahan (`git commit -m 'Add some AmazingFeature'`)
4. Push ke branch (`git push origin feature/AmazingFeature`)
5. Buat Pull Request

## 📄 Lisensi

Distributed under the MIT License. See `LICENSE` for more information.

## 📞 Support

Jika mengalami masalah atau memiliki pertanyaan, silakan buat issue di repository ini.

---

**Dibuat dengan ❤️ untuk pendidikan Indonesia**
