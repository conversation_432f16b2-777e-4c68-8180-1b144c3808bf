<?php
/**
 * Simple testing script untuk Sistem Absensi Siswa
 * Script ini akan melakukan basic testing pada komponen utama
 */

// Include config dan core files
require_once 'app/config/config.php';
require_once 'app/core/Database.php';
require_once 'app/core/Model.php';
require_once 'app/models/User.php';
require_once 'app/models/Guru.php';
require_once 'app/models/Kelas.php';
require_once 'app/models/Murid.php';

echo "<h1>🧪 Testing Sistem Absensi Siswa</h1>";
echo "<style>
body { font-family: Arial, sans-serif; max-width: 1000px; margin: 20px auto; padding: 20px; }
.test-pass { color: green; font-weight: bold; }
.test-fail { color: red; font-weight: bold; }
.test-section { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; }
.test-item { margin: 5px 0; padding: 5px; }
</style>";

$totalTests = 0;
$passedTests = 0;

function runTest($testName, $testFunction) {
    global $totalTests, $passedTests;
    $totalTests++;
    
    echo "<div class='test-item'>";
    echo "<strong>Test:</strong> $testName ... ";
    
    try {
        $result = $testFunction();
        if ($result) {
            echo "<span class='test-pass'>✅ PASS</span>";
            $passedTests++;
        } else {
            echo "<span class='test-fail'>❌ FAIL</span>";
        }
    } catch (Exception $e) {
        echo "<span class='test-fail'>❌ ERROR: " . $e->getMessage() . "</span>";
    }
    
    echo "</div>";
}

// Test 1: Database Connection
echo "<div class='test-section'>";
echo "<h3>🔌 Database Connection Tests</h3>";

runTest("Database Connection", function() {
    $db = Database::getInstance();
    return $db->getConnection() !== null;
});

runTest("Database Query Test", function() {
    $db = Database::getInstance();
    $result = $db->fetch("SELECT 1 as test");
    return $result['test'] == 1;
});

echo "</div>";

// Test 2: Model Tests
echo "<div class='test-section'>";
echo "<h3>📊 Model Tests</h3>";

runTest("User Model Initialization", function() {
    $userModel = new User();
    return $userModel instanceof User;
});

runTest("Guru Model Initialization", function() {
    $guruModel = new Guru();
    return $guruModel instanceof Guru;
});

runTest("Kelas Model Initialization", function() {
    $kelasModel = new Kelas();
    return $kelasModel instanceof Kelas;
});

runTest("Murid Model Initialization", function() {
    $muridModel = new Murid();
    return $muridModel instanceof Murid;
});

echo "</div>";

// Test 3: Data Integrity Tests
echo "<div class='test-section'>";
echo "<h3>🗃️ Data Integrity Tests</h3>";

runTest("Admin User Exists", function() {
    $userModel = new User();
    $admin = $userModel->findBy('username', 'admin');
    return $admin !== false && $admin['role'] === 'admin';
});

runTest("Default Kelas Exists", function() {
    $kelasModel = new Kelas();
    $kelas = $kelasModel->all('is_active = 1');
    return count($kelas) > 0;
});

runTest("Demo Guru Exists", function() {
    $userModel = new User();
    $guru = $userModel->findBy('username', 'guru1');
    return $guru !== false && $guru['role'] === 'guru';
});

echo "</div>";

// Test 4: Authentication Tests
echo "<div class='test-section'>";
echo "<h3>🔐 Authentication Tests</h3>";

runTest("Admin Authentication", function() {
    $userModel = new User();
    $result = $userModel->authenticate('admin', 'password');
    return $result !== false;
});

runTest("Guru Authentication", function() {
    $userModel = new User();
    $result = $userModel->authenticate('guru1', 'password');
    return $result !== false;
});

runTest("Invalid Authentication", function() {
    $userModel = new User();
    $result = $userModel->authenticate('invalid', 'invalid');
    return $result === false;
});

echo "</div>";

// Test 5: CRUD Operations Tests
echo "<div class='test-section'>";
echo "<h3>🔄 CRUD Operations Tests</h3>";

runTest("Create Test Kelas", function() {
    $kelasModel = new Kelas();
    $id = $kelasModel->create([
        'nama_kelas' => 'TEST-CLASS',
        'tingkat' => '99',
        'jurusan' => 'TEST',
        'tahun_ajaran' => '2024/2025'
    ]);
    
    // Clean up
    if ($id) {
        $kelasModel->delete($id);
    }
    
    return $id > 0;
});

runTest("Read Kelas Data", function() {
    $kelasModel = new Kelas();
    $kelas = $kelasModel->all('is_active = 1');
    return is_array($kelas) && count($kelas) > 0;
});

runTest("Update Test", function() {
    $kelasModel = new Kelas();
    
    // Create test data
    $id = $kelasModel->create([
        'nama_kelas' => 'TEST-UPDATE',
        'tingkat' => '99',
        'jurusan' => 'TEST',
        'tahun_ajaran' => '2024/2025'
    ]);
    
    if (!$id) return false;
    
    // Update
    $updated = $kelasModel->update($id, ['nama_kelas' => 'TEST-UPDATED']);
    
    // Verify
    $kelas = $kelasModel->find($id);
    $result = $kelas['nama_kelas'] === 'TEST-UPDATED';
    
    // Clean up
    $kelasModel->delete($id);
    
    return $result;
});

echo "</div>";

// Test 6: File System Tests
echo "<div class='test-section'>";
echo "<h3>📁 File System Tests</h3>";

runTest("Upload Directory Writable", function() {
    return is_writable(UPLOAD_PATH);
});

runTest("Upload Directory Exists", function() {
    return is_dir(UPLOAD_PATH);
});

runTest("Composer Autoload", function() {
    return file_exists('vendor/autoload.php');
});

runTest("Required PHP Extensions", function() {
    $required = ['pdo', 'pdo_mysql', 'mbstring'];
    foreach ($required as $ext) {
        if (!extension_loaded($ext)) {
            return false;
        }
    }
    return true;
});

echo "</div>";

// Test 7: Configuration Tests
echo "<div class='test-section'>";
echo "<h3>⚙️ Configuration Tests</h3>";

runTest("Database Configuration", function() {
    return defined('DB_HOST') && defined('DB_NAME') && defined('DB_USER');
});

runTest("Application Configuration", function() {
    return defined('APP_NAME') && defined('APP_URL');
});

runTest("Security Configuration", function() {
    return defined('SESSION_TIMEOUT') && defined('MAX_FILE_SIZE');
});

echo "</div>";

// Test Summary
echo "<div class='test-section'>";
echo "<h3>📊 Test Summary</h3>";
echo "<p><strong>Total Tests:</strong> $totalTests</p>";
echo "<p><strong>Passed:</strong> <span class='test-pass'>$passedTests</span></p>";
echo "<p><strong>Failed:</strong> <span class='test-fail'>" . ($totalTests - $passedTests) . "</span></p>";

$percentage = round(($passedTests / $totalTests) * 100, 2);
echo "<p><strong>Success Rate:</strong> $percentage%</p>";

if ($percentage >= 90) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>🎉 Excellent! System is ready to use.</h4>";
    echo "<p>All critical tests passed. The application is functioning properly.</p>";
    echo "</div>";
} elseif ($percentage >= 70) {
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>⚠️ Good, but some issues detected.</h4>";
    echo "<p>Most tests passed, but there are some issues that should be addressed.</p>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>❌ Critical issues detected!</h4>";
    echo "<p>Several tests failed. Please check the configuration and setup.</p>";
    echo "</div>";
}

echo "<p><a href='index.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to Application</a></p>";
echo "</div>";

echo "<hr>";
echo "<p><small>Test completed at: " . date('Y-m-d H:i:s') . "</small></p>";
?>
