<?php

class Murid extends Model
{
    protected $table = 'murid';
    
    public function getMuridWithKelas($id)
    {
        $sql = "SELECT m.*, k.nama_kelas, k.tingkat, k.jurusan, k.ta<PERSON>_ajaran
                FROM murid m
                JOIN kelas k ON m.kelas_id = k.id
                WHERE m.id = :id";
        
        return $this->db->fetch($sql, ['id' => $id]);
    }
    
    public function getAllMuridWithKelas($kelasId = null, $search = '')
    {
        $sql = "SELECT m.*, k.nama_kelas, k.tingkat, k.jurusan
                FROM murid m
                JOIN kelas k ON m.kelas_id = k.id
                WHERE m.is_active = 1";
        
        $params = [];
        
        if ($kelasId) {
            $sql .= " AND m.kelas_id = :kelas_id";
            $params['kelas_id'] = $kelasId;
        }
        
        if (!empty($search)) {
            $sql .= " AND (m.nama LIKE :search OR m.nis LIKE :search)";
            $params['search'] = "%{$search}%";
        }
        
        $sql .= " ORDER BY k.nama_kelas, m.nama";
        
        return $this->db->fetchAll($sql, $params);
    }
    
    public function getMuridByKelas($kelasId)
    {
        return $this->getAllMuridWithKelas($kelasId);
    }
    
    public function isNisExists($nis, $excludeId = null)
    {
        return $this->exists('nis', $nis, $excludeId);
    }
    
    public function importFromArray($data)
    {
        $this->db->beginTransaction();
        
        try {
            $imported = 0;
            $errors = [];
            
            foreach ($data as $index => $row) {
                try {
                    // Validate required fields
                    if (empty($row['nis']) || empty($row['nama']) || empty($row['kelas_id'])) {
                        $errors[] = "Row " . ($index + 1) . ": Missing required fields";
                        continue;
                    }
                    
                    // Check if NIS already exists
                    if ($this->isNisExists($row['nis'])) {
                        $errors[] = "Row " . ($index + 1) . ": NIS {$row['nis']} already exists";
                        continue;
                    }
                    
                    // Create murid
                    $this->create($row);
                    $imported++;
                    
                } catch (Exception $e) {
                    $errors[] = "Row " . ($index + 1) . ": " . $e->getMessage();
                }
            }
            
            if (empty($errors)) {
                $this->db->commit();
                return ['success' => true, 'imported' => $imported, 'errors' => []];
            } else {
                $this->db->rollback();
                return ['success' => false, 'imported' => 0, 'errors' => $errors];
            }
            
        } catch (Exception $e) {
            $this->db->rollback();
            return ['success' => false, 'imported' => 0, 'errors' => [$e->getMessage()]];
        }
    }
    
    public function getMuridForGuru($guruId, $kelasId = null)
    {
        $sql = "SELECT m.*, k.nama_kelas, k.tingkat, k.jurusan
                FROM murid m
                JOIN kelas k ON m.kelas_id = k.id
                JOIN guru_kelas gk ON k.id = gk.kelas_id
                WHERE gk.guru_id = :guru_id AND m.is_active = 1";
        
        $params = ['guru_id' => $guruId];
        
        if ($kelasId) {
            $sql .= " AND k.id = :kelas_id";
            $params['kelas_id'] = $kelasId;
        }
        
        $sql .= " ORDER BY k.nama_kelas, m.nama";
        
        return $this->db->fetchAll($sql, $params);
    }
    
    public function getStatsByKelas()
    {
        $sql = "SELECT k.nama_kelas, k.tingkat, k.jurusan,
                COUNT(m.id) as total_murid,
                SUM(CASE WHEN m.jenis_kelamin = 'L' THEN 1 ELSE 0 END) as laki_laki,
                SUM(CASE WHEN m.jenis_kelamin = 'P' THEN 1 ELSE 0 END) as perempuan
                FROM kelas k
                LEFT JOIN murid m ON k.id = m.kelas_id AND m.is_active = 1
                WHERE k.is_active = 1
                GROUP BY k.id
                ORDER BY k.tingkat, k.nama_kelas";
        
        return $this->db->fetchAll($sql);
    }
}
