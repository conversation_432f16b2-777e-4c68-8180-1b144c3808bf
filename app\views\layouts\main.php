<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?? APP_NAME ?></title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="<?= APP_URL ?>/public/css/style.css" rel="stylesheet">
    
    <?= $additionalCSS ?? '' ?>
</head>
<body>
    <?php if (isset($_SESSION['user_id'])): ?>
        <!-- Navigation -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container-fluid">
                <a class="navbar-brand" href="<?= APP_URL ?>/dashboard">
                    <i class="bi bi-mortarboard-fill"></i>
                    <?= APP_NAME ?>
                </a>
                
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link" href="<?= APP_URL ?>/dashboard">
                                <i class="bi bi-house"></i> Dashboard
                            </a>
                        </li>
                        
                        <?php if ($_SESSION['role'] === 'admin'): ?>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                    <i class="bi bi-gear"></i> Kelola Data
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="<?= APP_URL ?>/admin/guru">
                                        <i class="bi bi-person-badge"></i> Data Guru
                                    </a></li>
                                    <li><a class="dropdown-item" href="<?= APP_URL ?>/admin/kelas">
                                        <i class="bi bi-building"></i> Data Kelas
                                    </a></li>
                                    <li><a class="dropdown-item" href="<?= APP_URL ?>/admin/murid">
                                        <i class="bi bi-people"></i> Data Murid
                                    </a></li>
                                </ul>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="<?= APP_URL ?>/admin/rekap">
                                    <i class="bi bi-file-earmark-text"></i> Rekap Data
                                </a>
                            </li>
                        <?php elseif ($_SESSION['role'] === 'guru'): ?>
                            <li class="nav-item">
                                <a class="nav-link" href="<?= APP_URL ?>/guru/kelas">
                                    <i class="bi bi-building"></i> Kelas Saya
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                    
                    <ul class="navbar-nav">
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="bi bi-person-circle"></i>
                                <?= htmlspecialchars($_SESSION['nama']) ?>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><h6 class="dropdown-header">
                                    <?= ucfirst($_SESSION['role']) ?>
                                </h6></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="<?= APP_URL ?>/logout">
                                    <i class="bi bi-box-arrow-right"></i> Logout
                                </a></li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
    <?php endif; ?>
    
    <!-- Main Content -->
    <main class="<?= isset($_SESSION['user_id']) ? 'container-fluid mt-4' : '' ?>">
        <?= $content ?>
    </main>
    
    <!-- Footer -->
    <?php if (isset($_SESSION['user_id'])): ?>
        <footer class="bg-light text-center py-3 mt-5">
            <div class="container">
                <small class="text-muted">
                    &copy; <?= date('Y') ?> <?= APP_NAME ?> v<?= APP_VERSION ?>
                </small>
            </div>
        </footer>
    <?php endif; ?>
    
    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="<?= APP_URL ?>/public/js/app.js"></script>
    
    <?= $additionalJS ?? '' ?>
</body>
</html>
