<?php
$title = 'Dashboard - ' . APP_NAME;
ob_start();
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">Dashboard</h1>
                    <p class="text-muted">Selamat datang, <?= htmlspecialchars($user['nama']) ?>!</p>
                </div>
                <div class="text-end">
                    <small class="text-muted">
                        <i class="bi bi-clock"></i>
                        <?= date('d F Y, H:i') ?>
                    </small>
                </div>
            </div>
        </div>
    </div>
    
    <?php if ($user['role'] === 'admin'): ?>
        <!-- Admin Dashboard -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="card-title"><?= $stats['total_guru'] ?></h4>
                                <p class="card-text">Total Guru</p>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-person-badge" style="font-size: 2rem;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="card-title"><?= $stats['total_kelas'] ?></h4>
                                <p class="card-text">Total Kelas</p>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-building" style="font-size: 2rem;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="card-title"><?= $stats['total_murid'] ?></h4>
                                <p class="card-text">Total Murid</p>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-people" style="font-size: 2rem;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="card-title"><?= $stats['total_users'] ?></h4>
                                <p class="card-text">Total Users</p>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-people-fill" style="font-size: 2rem;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-clock-history"></i>
                            Murid Terbaru
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($stats['recent_murid'])): ?>
                            <div class="list-group list-group-flush">
                                <?php foreach ($stats['recent_murid'] as $murid): ?>
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong><?= htmlspecialchars($murid['nama']) ?></strong><br>
                                            <small class="text-muted"><?= htmlspecialchars($murid['nama_kelas']) ?></small>
                                        </div>
                                        <small class="text-muted">
                                            <?= date('d/m/Y', strtotime($murid['created_at'])) ?>
                                        </small>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php else: ?>
                            <p class="text-muted text-center">Belum ada data murid</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-speedometer2"></i>
                            Menu Cepat
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="<?= APP_URL ?>/admin/murid/add" class="btn btn-outline-primary">
                                <i class="bi bi-person-plus"></i> Tambah Murid
                            </a>
                            <a href="<?= APP_URL ?>/admin/murid/import" class="btn btn-outline-success">
                                <i class="bi bi-file-earmark-excel"></i> Import Excel
                            </a>
                            <a href="<?= APP_URL ?>/admin/rekap" class="btn btn-outline-info">
                                <i class="bi bi-file-earmark-text"></i> Lihat Rekap
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
    <?php elseif ($user['role'] === 'guru'): ?>
        <!-- Guru Dashboard -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="card-title"><?= $stats['total_kelas'] ?></h4>
                                <p class="card-text">Kelas Diampu</p>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-building" style="font-size: 2rem;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="card-title"><?= $stats['total_murid'] ?></h4>
                                <p class="card-text">Total Murid</p>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-people" style="font-size: 2rem;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <a href="<?= APP_URL ?>/guru/kelas" class="text-white text-decoration-none">
                            <i class="bi bi-eye" style="font-size: 2rem;"></i>
                            <p class="mt-2 mb-0">Lihat Kelas</p>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-building"></i>
                            Kelas yang Diampu
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($stats['kelas_list'])): ?>
                            <div class="row">
                                <?php foreach ($stats['kelas_list'] as $kelas): ?>
                                    <div class="col-md-4 mb-3">
                                        <div class="card border">
                                            <div class="card-body">
                                                <h6 class="card-title"><?= htmlspecialchars($kelas['nama_kelas']) ?></h6>
                                                <p class="card-text">
                                                    <small class="text-muted">
                                                        Tingkat <?= htmlspecialchars($kelas['tingkat']) ?> - 
                                                        <?= htmlspecialchars($kelas['jurusan']) ?>
                                                    </small><br>
                                                    <i class="bi bi-people"></i> <?= $kelas['total_murid'] ?> murid
                                                </p>
                                                <a href="<?= APP_URL ?>/guru/murid/<?= $kelas['id'] ?>" 
                                                   class="btn btn-sm btn-primary">
                                                    <i class="bi bi-eye"></i> Lihat Murid
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php else: ?>
                            <p class="text-muted text-center">Belum ada kelas yang diampu</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<?php
$content = ob_get_clean();
include VIEWS_PATH . '/layouts/main.php';
?>
