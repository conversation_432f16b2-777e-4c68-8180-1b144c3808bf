<?php

class NilaiCatatan extends Model
{
    protected $table = 'nilai_catatan';
    
    public function getNilaiCatatanWithDetails($muridId, $guruId = null)
    {
        $sql = "SELECT nc.*, m.nama as nama_murid, m.nis, g.nama as nama_guru,
                k.nama_kelas, k.ting<PERSON>, k.jurusan
                FROM nilai_catatan nc
                JOIN murid m ON nc.murid_id = m.id
                JOIN guru g ON nc.guru_id = g.id
                JOIN kelas k ON m.kelas_id = k.id
                WHERE nc.murid_id = :murid_id";
        
        $params = ['murid_id' => $muridId];
        
        if ($guruId) {
            $sql .= " AND nc.guru_id = :guru_id";
            $params['guru_id'] = $guruId;
        }
        
        $sql .= " ORDER BY nc.tanggal DESC, nc.created_at DESC";
        
        return $this->db->fetchAll($sql, $params);
    }
    
    public function getRekapByKelas($kelasId, $guruId = null)
    {
        $sql = "SELECT m.id, m.nis, m.nama, m.jenis_kelamin,
                COUNT(CASE WHEN nc.jenis = 'nilai' THEN 1 END) as total_nilai,
                AVG(CASE WHEN nc.jenis = 'nilai' THEN nc.nilai END) as rata_nilai,
                COUNT(CASE WHEN nc.jenis = 'catatan' THEN 1 END) as total_catatan,
                COUNT(CASE WHEN nc.jenis = 'absensi' THEN 1 END) as total_absensi,
                MAX(nc.tanggal) as last_update
                FROM murid m
                LEFT JOIN nilai_catatan nc ON m.id = nc.murid_id";
        
        $params = ['kelas_id' => $kelasId];
        
        if ($guruId) {
            $sql .= " AND nc.guru_id = :guru_id";
            $params['guru_id'] = $guruId;
        }
        
        $sql .= " WHERE m.kelas_id = :kelas_id AND m.is_active = 1
                  GROUP BY m.id
                  ORDER BY m.nama";
        
        return $this->db->fetchAll($sql, $params);
    }
    
    public function addNilai($muridId, $guruId, $mataPelajaran, $nilai, $tanggal)
    {
        return $this->create([
            'murid_id' => $muridId,
            'guru_id' => $guruId,
            'mata_pelajaran' => $mataPelajaran,
            'jenis' => 'nilai',
            'nilai' => $nilai,
            'tanggal' => $tanggal
        ]);
    }
    
    public function addCatatan($muridId, $guruId, $mataPelajaran, $catatan, $tanggal)
    {
        return $this->create([
            'murid_id' => $muridId,
            'guru_id' => $guruId,
            'mata_pelajaran' => $mataPelajaran,
            'jenis' => 'catatan',
            'catatan' => $catatan,
            'tanggal' => $tanggal
        ]);
    }
    
    public function addAbsensi($muridId, $guruId, $mataPelajaran, $catatan, $tanggal)
    {
        return $this->create([
            'murid_id' => $muridId,
            'guru_id' => $guruId,
            'mata_pelajaran' => $mataPelajaran,
            'jenis' => 'absensi',
            'catatan' => $catatan,
            'tanggal' => $tanggal
        ]);
    }
    
    public function getNilaiByMuridAndMapel($muridId, $mataPelajaran, $guruId = null)
    {
        $sql = "SELECT * FROM nilai_catatan 
                WHERE murid_id = :murid_id 
                AND mata_pelajaran = :mata_pelajaran 
                AND jenis = 'nilai'";
        
        $params = [
            'murid_id' => $muridId,
            'mata_pelajaran' => $mataPelajaran
        ];
        
        if ($guruId) {
            $sql .= " AND guru_id = :guru_id";
            $params['guru_id'] = $guruId;
        }
        
        $sql .= " ORDER BY tanggal DESC";
        
        return $this->db->fetchAll($sql, $params);
    }
    
    public function getStatsByGuru($guruId, $startDate = null, $endDate = null)
    {
        $sql = "SELECT 
                COUNT(CASE WHEN jenis = 'nilai' THEN 1 END) as total_nilai,
                COUNT(CASE WHEN jenis = 'catatan' THEN 1 END) as total_catatan,
                COUNT(CASE WHEN jenis = 'absensi' THEN 1 END) as total_absensi,
                COUNT(DISTINCT murid_id) as total_murid_dinilai,
                AVG(CASE WHEN jenis = 'nilai' THEN nilai END) as rata_nilai
                FROM nilai_catatan 
                WHERE guru_id = :guru_id";
        
        $params = ['guru_id' => $guruId];
        
        if ($startDate && $endDate) {
            $sql .= " AND tanggal BETWEEN :start_date AND :end_date";
            $params['start_date'] = $startDate;
            $params['end_date'] = $endDate;
        }
        
        return $this->db->fetch($sql, $params);
    }
}
